import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  buildAuditTrailEntry,
  finalizeAuditTrailEntry,
  generateEvents,
  setAuditMeta,
} from '#src/utils/audit-trail.util.js';
import Fastify from 'fastify';
import { deepRedact } from '#src/utils/response-modifier.util.js';
import geoip from 'geoip-lite';
import { setCache } from '#src/utils/cache.util.js';

vi.mock('../modules/audit-trail/constants/index.js', () => ({
  EventActionsConstant: {
    EVENT_ACTION_DESCRIPTIONS: {
      VIEWED: {
        name: (formattedEvent) => `${formattedEvent} Viewed`,
        description: (username, formattedEvent, referenceIds) =>
          `${username} viewed the ${formattedEvent} list.`,
      },
      CREATED: {
        name: (formattedEvent) => `${formattedEvent} Created`,
        description: (username, formattedEvent) => `${username} created a new ${formattedEvent}.`,
      },
      UPDATED: {
        name: (formattedEvent) => `${formattedEvent} Updated`,
        description: (username, formattedEvent, referenceIds) =>
          `${username} updated the ${formattedEvent} (ID: ${referenceIds}).`,
      },
      DEFAULT: {
        name: (formattedEvent) => `${formattedEvent} Action`,
        description: (username, formattedEvent) =>
          `${username} performed an action on ${formattedEvent}.`,
      },
      // Add other action descriptions here if your actual constant has them
    },
  },
}));

vi.mock('#src/utils/cache.util.js', () => ({
  setCache: vi.fn(),
}));

describe('Test case for generateEvents', () => {
  let mockRequestForEvents;

  beforeEach(() => {
    mockRequestForEvents = {
      authInfo: { username: 'test_user' },
      entity: { hierarchy: 'test_hierarchy', name: 'test_entity' },
      auditEntries: {
        module: 'some_module_base',
        event: 'some_event_base',
        action: 'DEFAULT',
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should generate event correctly for VIEWED action', async () => {
    mockRequestForEvents.auditEntries.module = 'module_name';
    mockRequestForEvents.auditEntries.event = 'event_name';
    mockRequestForEvents.auditEntries.action = 'VIEWED';

    const referenceId = '123';
    const status = 'Success';

    const result = await generateEvents(mockRequestForEvents, referenceId, status);

    expect(result).toEqual({
      moduleName: 'Module Name',
      name: 'Event Name Viewed',
      description: 'test_user viewed the Event Name list.',
    });
  });

  it('should generate event correctly for CREATED action', async () => {
    mockRequestForEvents.auditEntries.module = 'module_name';
    mockRequestForEvents.auditEntries.event = 'event_name';
    mockRequestForEvents.auditEntries.action = 'CREATED';

    const referenceId = '123';
    const status = 'Success';

    const result = await generateEvents(mockRequestForEvents, referenceId, status);
    expect(result).toEqual({
      moduleName: 'Module Name',
      name: 'Event Name Created',
      description: 'test_user created a new Event Name.',
    });
  });

  it('should handle unknown actions and return default', async () => {
    mockRequestForEvents.auditEntries.module = 'module_name';
    mockRequestForEvents.auditEntries.event = 'event_name';
    mockRequestForEvents.auditEntries.action = 'NON_EXISTENT_ACTION';

    const referenceId = '123';
    const status = 'Success';

    const result = await generateEvents(mockRequestForEvents, referenceId, status);
    expect(result).toEqual({
      moduleName: 'Module Name',
      name: 'Event Name Action',
      description: 'test_user performed an action on Event Name.',
    });
  });

  it('should handle missing request values gracefully', async () => {
    const minimalRequest = {
      auditEntries: {
        module: 'some_module',
        event: 'some_event',
        action: 'VIEWED',
      },
    };
    const referenceId = '123';
    const status = 'Success';

    const result = await generateEvents(minimalRequest, referenceId, status);
    expect(result).toEqual({
      moduleName: 'Some Module',
      name: 'Some Event Viewed',
      description: 'UNKNOWN_USER viewed the Some Event list.',
    });
  });

  it('should handle edge cases in the event description', async () => {
    mockRequestForEvents.auditEntries.module = 'module_name';
    mockRequestForEvents.auditEntries.event = 'event_name_with_special_chars!@#';
    mockRequestForEvents.auditEntries.action = 'UPDATED';

    const referenceId = '123';
    const status = 'Success';

    const result = await generateEvents(mockRequestForEvents, referenceId, status);
    expect(result).toEqual({
      moduleName: 'Module Name',
      name: 'Event Name With Special Chars!@# Updated',
      description: 'test_user updated the Event Name With Special Chars!@# (ID: 123).',
    });
  });
});

describe('Test case for audit trail', () => {
  let mockRequest = {};
  let mockReply = {};
  let fastify;

  beforeEach(async () => {
    fastify = Fastify();
    fastify.config = { KAFKA: true };

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Mock Kafka producer
    fastify.kafka = {
      producer: {
        send: vi.fn(),
      },
    };

    // Mock Redis
    fastify.redis = {};

    // Reset mock reply object
    mockReply = {
      statusCode: 200,
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should populate audit trail in the request object', async () => {
    const initialMockRequest = {
      headers: {
        origin: 'https://bo.example.com/123456789012/merchant',
        'x-forwarded-for': '*********',
        'x-user-agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      },
      id: 'test_request_id',
      url: '/test/path',
      method: 'GET',
      query: { param1: 'value1' },
      body: { key: 'value' },
      fingerprintId: 'test_fingerprint_id',
      authInfo: {
        authAccess: 'User',
        id: 'user_id_123',
        username: 'testuser',
        role: 'Admin',
        department: 'IT',
      },
      entity: {
        id: 'entity_id_456',
        hierarchy: 'merchant',
        name: 'Test Merchant',
        project_code: 'TM001',
        prefix: 'TM',
      },
      parentEntity: {
        id: 'org_id_789',
        name: 'Test Organization',
        project_code: 'TO001',
        prefix: 'TO',
      },
    };

    vi.spyOn(geoip, 'lookup').mockReturnValueOnce({
      city: 'TestCity',
      country: 'TC',
    });

    await buildAuditTrailEntry(fastify, initialMockRequest);

    expect(initialMockRequest.auditEntries).toHaveProperty('timestamp');
    expect(initialMockRequest.auditEntries.entityAccessId).toBe('entity_id_456');
    expect(initialMockRequest.auditEntries.hierarchyLevel).toBe('merchant');
    expect(initialMockRequest.auditEntries.module).toBeNull();
    expect(initialMockRequest.auditEntries.event).toBeNull();
    expect(initialMockRequest.auditEntries.action).toBeNull();
    expect(initialMockRequest.auditEntries.actor.userType).toBe('User');
    expect(initialMockRequest.auditEntries.actor.userId).toBe('user_id_123');
    expect(initialMockRequest.auditEntries.actor.username).toBe('testuser');
    expect(initialMockRequest.auditEntries.actor.role).toBe('Admin');
    expect(initialMockRequest.auditEntries.actor.department).toBe('IT');
    expect(initialMockRequest.auditEntries.actor.organization).toEqual({
      id: 'org_id_789',
      code: 'TO001',
      prefix: 'TO',
      name: 'Test Organization',
    });
    expect(initialMockRequest.auditEntries.actor.merchant).toEqual({
      id: 'entity_id_456',
      code: 'TM001',
      prefix: 'TM',
      name: 'Test Merchant',
    });
    expect(initialMockRequest.auditEntries.target).toEqual([]);
    expect(initialMockRequest.auditEntries.details.request.requestId).toBe('test_request_id');
    expect(initialMockRequest.auditEntries.details.request.statusCode).toBeNull();
    expect(initialMockRequest.auditEntries.details.request.path).toBe('/test/path');
    expect(initialMockRequest.auditEntries.details.request.method).toBe('GET');
    expect(initialMockRequest.auditEntries.details.request.parameters).toEqual({
      param1: 'value1',
    });
    expect(initialMockRequest.auditEntries.details.request.payload).toEqual({ key: 'value' });
    expect(initialMockRequest.auditEntries.details.error).toEqual({});
    expect(initialMockRequest.auditEntries.details.metrics).toEqual({});
    expect(initialMockRequest.auditEntries.context.ip).toBe('*********');
    expect(initialMockRequest.auditEntries.context.location).toBe('TestCity, TC');
    expect(initialMockRequest.auditEntries.context.userAgent).toBe(
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    );
    expect(initialMockRequest.auditEntries.context.device).toBe('Mac');
    expect(initialMockRequest.auditEntries.context.fingerprintId).toBe('test_fingerprint_id');
    expect(initialMockRequest.auditEntries.context.host).toBe(
      'https://bo.example.com/123456789012/merchant',
    );
    expect(initialMockRequest.auditEntries.status).toBeNull();
    expect(initialMockRequest.auditEntries.description).toBeNull();
  });

  it('should return UNKNOWN_LOCATION when IP is missing', async () => {
    const initialMockRequest = {
      headers: {},
      id: 'test_id',
      url: 'test_url',
      method: 'GET',
      query: {},
      body: {},
      fingerprintId: 'fid',
      authInfo: {},
      entity: {},
      parentEntity: {},
    };

    vi.spyOn(geoip, 'lookup').mockReturnValue(null);

    await buildAuditTrailEntry(fastify, initialMockRequest);

    expect(initialMockRequest.auditEntries.context.location).toBe('UNKNOWN_LOCATION');
  });

  it('should fallback to UNKNOWN_CITY and UNKNOWN_COUNTRY if geo data missing', async () => {
    const initialMockRequest = {
      headers: {
        'x-forwarded-for': '*********',
      },
      id: 'test_id',
      url: 'test_url',
      method: 'GET',
      query: {},
      body: {},
      fingerprintId: 'fid',
      authInfo: {},
      entity: {},
      parentEntity: {},
    };

    vi.spyOn(geoip, 'lookup').mockReturnValueOnce({
      city: undefined,
      country: undefined,
    });

    await buildAuditTrailEntry(fastify, initialMockRequest);

    expect(initialMockRequest.auditEntries.context.location).toBe('UNKNOWN_CITY, UNKNOWN_COUNTRY');
  });

  it('should detect Windows PC from user agent', async () => {
    const initialMockRequest = {
      headers: {
        'x-user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      },
      id: 'test_id',
      url: 'test_url',
      method: 'GET',
      query: {},
      body: {},
      fingerprintId: 'fid',
      authInfo: {},
      entity: {},
      parentEntity: {},
    };

    await buildAuditTrailEntry(fastify, initialMockRequest);

    expect(initialMockRequest.auditEntries.context.device).toBe('Windows PC');
  });

  it('should send audit trail to Kafka successfully without fallback to Redis', async () => {
    fastify.kafka.producer.send.mockResolvedValueOnce(true);

    mockRequest = {
      auditEntries: {
        status: null,
        details: {
          request: {
            statusCode: null,
          },
          error: {},
        },
      },
      authInfo: { id: 'user123' },
      entity: { id: 'access123' },
      responsePayload: null,
    };

    await finalizeAuditTrailEntry(fastify, mockRequest, mockReply);

    expect(fastify.kafka.producer.send).toHaveBeenCalledWith({
      topic: 'audit-trails',
      messages: expect.any(Array),
    });
    expect(setCache).not.toHaveBeenCalled();
    expect(mockRequest.auditEntries.status).toBe('Success');
    expect(mockRequest.auditEntries.details.request.statusCode).toBe(200);
  });

  it('should handle Kafka send failure by storing audit trail in Redis', async () => {
    fastify.kafka.producer.send.mockRejectedValueOnce(new Error('Kafka connection lost'));

    mockRequest = {
      authInfo: { id: 'user123' },
      auditEntries: {
        status: null,
        details: {
          request: { statusCode: null },
          error: {},
        },
      },
      entity: { id: 'access123' },
      responsePayload: {
        message: 'Something went wrong',
        errorCode: 'ERR_500',
      },
    };
    mockReply.statusCode = 500;

    await finalizeAuditTrailEntry(fastify, mockRequest, mockReply);

    expect(fastify.kafka.producer.send).toHaveBeenCalled();
    expect(setCache).toHaveBeenCalled();

    expect(setCache).toHaveBeenCalledWith(
      fastify.redis,
      expect.stringMatching(/^failed-kafka-message:access123:user123:/),
      expect.any(Array),
    );

    expect(fastify.log.debug).toHaveBeenCalledWith(expect.any(Error), 'Kafka send failed');

    expect(fastify.log.debug).toHaveBeenCalledWith(
      expect.any(Array),
      'Kafka message stored in Redis',
    );
    expect(mockRequest.auditEntries.status).toBe('Failed');
    expect(mockRequest.auditEntries.details.request.statusCode).toBe(500);
    expect(mockRequest.auditEntries.details.error.message).toBe('Something went wrong');
    expect(mockRequest.auditEntries.details.error.errorCode).toBe('ERR_500');
  });

  it('should redact sensitive fields', () => {
    const testRequest = {
      authInfo: {
        id: 'user123',
        password: `password-${Date.now()}`,
        confirmPassword: `password-${Date.now()}`,
        username: 'test_redact_user',
      },
      someOtherField: 'someValue',
    };

    const redactedAuditEntry = deepRedact(testRequest);

    expect(redactedAuditEntry.authInfo.password).toBe('[REDACTED]');
    expect(redactedAuditEntry.authInfo.confirmPassword).toBe('[REDACTED]');
    expect(redactedAuditEntry.authInfo.id).toBe('user123');
    expect(redactedAuditEntry.authInfo.username).toBe('test_redact_user');
  });

  it('should not send audit trail if Kafka is not configured', async () => {
    fastify.config.KAFKA = false;

    mockRequest = {
      auditEntries: {
        status: null,
        details: {
          request: { statusCode: null },
        },
      },
      authInfo: { id: 'user123' },
      entity: { id: 'access123' },
    };

    await finalizeAuditTrailEntry(fastify, mockRequest, mockReply);

    expect(fastify.kafka.producer.send).not.toHaveBeenCalled();
    expect(setCache).not.toHaveBeenCalled();
    expect(fastify.log.debug).not.toHaveBeenCalledWith(expect.any(Error), 'Kafka send failed');
    expect(fastify.log.debug).not.toHaveBeenCalledWith(
      expect.any(Array),
      'Kafka message stored in Redis',
    );
  });

  describe('setAuditMeta', () => {
    let mockRequestWithAuditEntries;

    beforeEach(() => {
      mockRequestWithAuditEntries = {
        auditEntries: {
          module: null,
          event: null,
          action: null,
          description: null,
        },
        authInfo: { username: 'testuser' },
        entity: { hierarchy: 'org', name: 'Test Org' },
      };
    });

    it('should set audit trail metadata and description', async () => {
      const meta = { module: 'USER', event: 'Settings', action: 'CREATED' };
      const referenceId = 'some-user-id';

      await setAuditMeta(mockRequestWithAuditEntries, meta, referenceId);

      expect(mockRequestWithAuditEntries.auditEntries.module).toBe('USER');
      expect(mockRequestWithAuditEntries.auditEntries.event).toBe('Settings');
      expect(mockRequestWithAuditEntries.auditEntries.action).toBe('CREATED');
      expect(mockRequestWithAuditEntries.auditEntries.description).toBe(
        'testuser created a new Settings.',
      );
    });

    it('should initialize auditEntries if not present', async () => {
      const emptyRequest = {
        authInfo: { username: 'testuser' },
        entity: { hierarchy: 'org', name: 'Test Org' },
      };
      const meta = { module: 'USER', event: 'Settings', action: 'CREATED' };

      await setAuditMeta(emptyRequest, meta);

      expect(emptyRequest.auditEntries).toBeDefined();
      expect(emptyRequest.auditEntries.module).toBe('USER');
      expect(emptyRequest.auditEntries.event).toBe('Settings');
      expect(emptyRequest.auditEntries.action).toBe('CREATED');
      expect(emptyRequest.auditEntries.description).toBe('testuser created a new Settings.');
    });
  });
});
