import { AuditTrailError } from '#src/modules/audit-trail/errors/index.js';
import { AuditTrailRepository } from '#src/modules/audit-trail/repository/index.js';
import { BulkJobRepository } from '#src/modules/bulk-job/repository/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';

/**
 * Retrieves a list of audit trails based on the provided request parameters.
 *
 * @param {Object} request - The request object containing query parameters.
 * @param {string} [request.query.filter_timestamp_gte] - The start timestamp for filtering audit trails.
 * @param {string} [request.query.filter_timestamp_lte] - The end timestamp for filtering audit trails.
 *
 * @returns {Promise<Object>} - A promise that resolves to the result object containing audit trails.
 * @throws {AuditTrailError} - If the start and end timestamps are not within the same month.
 * @throws {CoreError} - If an error occurs while retrieving audit trails or if no audit trails are found.
 */
export const index = async (request) => {
  const startDateTime = request.query.filter_timestamp_gte
    ? new Date(request.query.filter_timestamp_gte)
    : new Date(Date.now() - 15 * 60 * 1000);

  const endDateTime = request.query.filter_timestamp_lte
    ? new Date(request.query.filter_timestamp_lte)
    : new Date();

  const start = new Date(startDateTime);
  const end = new Date(endDateTime);

  // Audit logs are stored monthly; querying across months is not allowed.
  if (start.getFullYear() !== end.getFullYear() || start.getMonth() !== end.getMonth()) {
    throw AuditTrailError.invalidDate();
  }

  const result = await AuditTrailRepository.findAll(request, { startDateTime });

  if (result?.error) {
    throw CoreError.unknownError(result?.error);
  }

  if (!result.rows[0]) {
    throw CoreError.notFound(MODULE_NAMES.AUDIT_TRAIL);
  }

  return result;
};

/**
 * Retrieves a single audit trail based on the provided ID.
 *
 * @param {Object} request - The request object containing authentication and authorization information.
 * @param {string} id - The unique identifier of the audit trail to retrieve.
 *
 * @returns {Promise<Object>} - A promise that resolves to the retrieved audit trail object.
 * @throws {CoreError} - If the audit trail is not found.
 */
export const view = async (request, id) => {
  const result = await AuditTrailRepository.findById(request, id);

  if (!result[0]) {
    throw CoreError.notFound(MODULE_NAMES.AUDIT_TRAIL);
  }

  return result[0];
};

/**
 * Creates a new bulk job to export audit trails based on the provided request parameters.
 *
 * @param {Object} request - The request object containing authentication and authorization information.
 *
 * @returns {Promise<Object>} - A promise that resolves to the created bulk job object.
 * @throws {CoreError} - If an error occurs while creating the bulk job.
 */
export const exportAuditTrail = async (request) => {
  const result = await BulkJobRepository.create(
    request.server,
    {
      entityId: request.entity.id,
      type: 'export',
      model: 'audit_trail',
      title: 'Export audit trails',
      parameters: request.query || {},
      status: 'pending',
    },
    request.authInfo,
  );

  if (result?.error) {
    throw CoreError.unknownError(result?.error);
  }

  return result;
};
