import transformText from '@/utils/text-transform.util';
import type { ChangeCaseTransform } from '../../typography/Typography.type';

/**
 * Formats current datetime for file naming
 */
export const formatDateTime = () =>
  // TODO: Handle user timezone and locale
  new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').slice(0, -5);

/**
 * Formats a filename with the current datetime and optional suffix.
 */
export const getFormattedFileName = (baseFileName: string, suffix?: string) => {
  const datetime = formatDateTime();
  if (!suffix) return `${baseFileName}_${datetime}`;

  return `${baseFileName}_${suffix}_${datetime}`;
};

/**
 * Helper function to create a header name from a field name with translation and text transformation
 * @param fieldName - The field name to transform
 * @param t - Translation function
 * @returns Transformed header name
 */
export const createHeaderName = (
  fieldName: string,
  t: (key: string) => string,
  transform?: ChangeCaseTransform
): string => {
  return transformText(t(fieldName), transform ?? 'sentenceCase');
};

/**
 * Function to translate string for DataGrid
 */
export const useLocaleText = (t: (key: string) => string) => {
  return {
    // Columns management text
    columnsManagementShowHideAllText: t('datagrid.columnsManagement.showHideAll'),
    columnsManagementReset: t('datagrid.columnsManagement.reset'),

    // Filter panel text
    filterPanelAddFilter: t('datagrid.filterPanel.addFilter'),
    filterPanelRemoveAll: t('datagrid.filterPanel.removeAll'),
    filterPanelInputLabel: t('datagrid.filterPanel.inputLabel'),
    filterPanelInputPlaceholder: t('datagrid.filterPanel.inputPlaceholder'),
    filterPanelOperator: t('datagrid.filterPanel.operator'),
    filterPanelOperatorAnd: t('datagrid.filterPanel.add'),
    filterPanelOperatorOr: t('datagrid.filterPanel.or'),
    filterPanelColumns: t('datagrid.filterPanel.columns'),

    // Filter panel operator dropdown options
    filterOperatorContains: t('datagrid.filterOperator.contains'),
    filterOperatorDoesNotContain: t('datagrid.filterOperator.doesNotContain'),
    filterOperatorEquals: t('datagrid.filterOperator.equals'),
    filterOperatorDoesNotEqual: t('datagrid.filterOperator.doesNotEqual'),
    filterOperatorStartsWith: t('datagrid.filterOperator.startsWith'),
    filterOperatorEndsWith: t('datagrid.filterOperator.endsWith'),
    filterOperatorIsEmpty: t('datagrid.filterOperator.isEmpty'),
    filterOperatorIsNotEmpty: t('datagrid.filterOperator.isNotEmpty'),
    filterOperatorIsAnyOf: t('datagrid.filterOperator.isAnyOf'),

    // Pagination
    paginationRowsPerPage: t('datagrid.pagination.rowsPerPage:'),
    paginationDisplayedRows: ({
      from,
      to,
      count,
      estimated,
    }: {
      from: number;
      to: number;
      count: number;
      estimated: number | undefined;
    }) => {
      const around = t('datagrid.pagination.around');
      const moreThan = t('datagrid.pagination.moreThan');
      const of = t('datagrid.pagination.of');
      if (!estimated) {
        const countLabel = count !== -1 ? count : `${moreThan} ${to}`;
        return `${from}–${to} ${of} ${countLabel}`;
      }
      const estimatedLabel =
        estimated && estimated > to ? `${around} ${estimated}` : `${moreThan} ${to}`;
      return `${from}–${to} ${of} ${count !== -1 ? count : estimatedLabel}`;
    },
  };
};
