import {
  StyledQuickFilter,
  StyledText<PERSON>ield,
  StyledToolbarButton,
} from '@/components/base/data-display/datagrid/styles/QuickFilter.style';
import Cancel from '@mui/icons-material/Cancel';
import Search from '@mui/icons-material/Search';
import InputAdornment from '@mui/material/InputAdornment';
import Tooltip from '@mui/material/Tooltip';
import {
  QuickFilterClear,
  QuickFilterControl,
  QuickFilterTrigger,
  type GridToolbarQuickFilterProps,
} from '@mui/x-data-grid-premium';
import { useTranslation } from 'react-i18next';

/**
 * CustomQuickFilter component provides a search functionality for DataGrid
 * with expandable search input and clear button
 */
const CustomQuickFilter = ({
  quickFilterProps,
}: {
  quickFilterProps: GridToolbarQuickFilterProps;
}) => {
  const { t } = useTranslation();

  return (
    <StyledQuickFilter
      expanded
      {...quickFilterProps}
    >
      <QuickFilterTrigger
        render={(triggerProps, state) => (
          <Tooltip
            title={t('common.label.search')}
            enterDelay={0}
          >
            <StyledToolbarButton
              {...triggerProps}
              ownerState={{ expanded: state.expanded }}
              color="default"
            >
              <Search />
            </StyledToolbarButton>
          </Tooltip>
        )}
      />
      <QuickFilterControl
        render={({ ref, ...controlProps }, state) => (
          <StyledTextField
            {...controlProps}
            ownerState={{ expanded: state.expanded }}
            inputRef={ref}
            aria-label="Search"
            placeholder={t('common.label.searchKeywords')}
            size="small"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
                endAdornment: state.value ? (
                  <InputAdornment position="end">
                    <QuickFilterClear
                      edge="end"
                      size="small"
                      aria-label="Clear search"
                      material={{ sx: { marginRight: -0.75 } }}
                    >
                      <Cancel fontSize="small" />
                    </QuickFilterClear>
                  </InputAdornment>
                ) : null,
                ...controlProps.slotProps?.input,
              },
              ...controlProps.slotProps,
            }}
          />
        )}
      />
    </StyledQuickFilter>
  );
};

export default CustomQuickFilter;
