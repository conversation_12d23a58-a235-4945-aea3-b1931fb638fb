import { getByXPath } from '@/utils/xpath-selector.util';
import { DataGridPremium, Toolbar, type GridPremiumSlotsComponent } from '@mui/x-data-grid-premium';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, screen, waitFor, within } from 'storybook/test';
import CustomExportButton from './CustomExportButton';

/**
 * Storybook metadata configuration for the CustomExportButton component.
 */
const meta = {
  component: CustomExportButton,
  title: 'Components/base/data-display/datagrid/CustomExportButton',
  tags: ['autodocs'],
  decorators: [
    (Story) => {
      const CustomToolbarComponent = () => (
        <Toolbar>
          <Story />
        </Toolbar>
      );

      const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
        toolbar: CustomToolbarComponent,
      };
      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={defaultSlotProps}
        />
      );
    },
  ],
} satisfies Meta<typeof CustomExportButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomExportButton component.
 */
export const Default: Story = {
  args: {
    csvOptions: {
      fileName: 'export',
    },
  },
  play: async ({ canvas, userEvent, step }) => {
    await step('Able to show tooltip', async () => {
      await userEvent.hover(canvas.getByText('common.button.export'));

      const tooltip = await waitFor(() =>
        getByXPath(
          `//div[@role="tooltip" and contains(@class, "MuiTooltip-popper") and contains(., "common.label.selectExport")]`,
          document
        )
      );

      expect(tooltip).toBeInTheDocument();
    });

    await step('Click export button', async () => {
      await userEvent.click(canvas.getByText('common.button.export'));
    });

    const menu = await waitFor(() => screen.getByRole('menu'));
    await step('Menu is shown when clicked', async () => {
      expect(menu).toBeInTheDocument();
    });

    await step('Menu item is correct', async () => {
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(3);

      expect(within(menu).getByText(/current page/i)).toBeInTheDocument();
      expect(within(menu).getByText(/all/i)).toBeInTheDocument();
      expect(within(menu).getByText(/filtered/i)).toBeInTheDocument();
    });

    await step('Menu can be closed with escape button', async () => {
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
      });
    });
  },
};
