export const ACCESS_LEVELS = {
  MERCHANT: 'merchant',
  ORGANIZATION: 'organization',
  ROOT: 'root',
  USER: 'user',
};

export const ACCESS_LEVEL_KEYS = {
  user: 'user',
  member: 'member',
  webhook: 'webhook',
};

export const CACHE_SECOND = {
  SHORT: 10, // For rapidly changing data or debounce-type caching
  MEDIUM: 30, // 30 second – suitable for moderately volatile data
  STANDARD: 60, // 1 minute – good default for most general cache
  LONG: 3600, // 1 hour – stable data that changes infrequently
  DAILY: 86400, // 24 hours – rarely changing reference data
  WEEKLY: 604800, // 7 days – archive-type or external lookup cache
  NEVER: 0, // Used when caching is disabled
};

export const COMMON_STATUSES = {
  ACTIVE: 'active',
  DELETED: 'deleted',
  INACTIVE: 'inactive',
};

export const EVENTS = {
  APP_CENTER: 'APP_CENTER',
  AUDIT_TRAIL: 'AUDIT_TRAIL',
  AUTOMATION: 'AUTOMATION',
  DEVELOPER_HUB: 'DEVELOPER_HUB',
  GAME_PROVIDER: 'GAME_PROVIDER',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  MAINTENANCE: 'MAINTENANCE',
  MANAGE_MONITOR: 'MANAGE_MONITOR',
  MEDIA: 'MEDIA',
  MEMBER: 'MEMBER',
  MEMBER_POINT: 'MEMBER_POINT',
  MEMBER_VIP: 'MEMBER_VIP',
  MERCHANT: 'MERCHANT',
  MERCHANT_CREDIT: 'MERCHANT_CREDIT',
  ORGANIZATION: 'ORGANIZATION',
  OTP: 'OTP',
  PERSONAL_SETTINGS: 'PERSONAL_SETTINGS',
  REGISTRATION_FORM: 'REGISTRATION_FORM',
  RESET_PASSWORD: 'RESET_PASSWORD',
  RISK_GROUP: 'RISK_GROUP',
  ROLE: 'ROLE',
  SETTINGS: {
    PERSONAL: 'SYSTEM_PERSONAL_SETTINGS',
    SAFETY: 'SYSTEM_SECURITY_SAFETY',
    THEME: 'SYSTEM_THEME_SETTINGS',
  },
  SYSTEM_CREDIT_LIMIT_SETTINGS: 'SYSTEM_CREDIT_LIMIT_SETTINGS',
  SYSTEM_CURRENCY_SETTINGS: 'SYSTEM_CURRENCY_SETTINGS',
  SYSTEM_DATA_MASKING_SETTINGS: 'SYSTEM_DATA_MASKING_SETTINGS',
  SYSTEM_LANGUAGE_SETTINGS: 'SYSTEM_LANGUAGE_SETTINGS',
  SYSTEM_LOCALISATION_SETTINGS: 'SYSTEM_LOCALISATION_SETTINGS',
  SETTINGS_OPTIONS: {
    PERSONAL: 'PERSONAL_SETTINGS_OPTIONS',
    SAFETY: 'SECURITY_SAFETY_SETTINGS_OPTIONS',
    THEME: 'THEME_SETTINGS_OPTIONS',
  },
  SYSTEM_REGION_SETTINGS: 'SYSTEM_REGION_SETTINGS',
  SYSTEM_SECURITY_ACCESS_CONTROL: 'SYSTEM_SECURITY_ACCESS_CONTROL',
  TAGS: 'TAGS',
  TRANSACTIONS: 'TRANSACTIONS',
  TRIGGERED_EVENT_LOG: 'TRIGGERED_EVENT_LOG ',
  TWO_FACTOR_AUTHENTICATION: 'TWO_FACTOR_AUTHENTICATION',
  USER: 'USER',
  USER_AUDIT_TRAIL: 'USER_AUDIT_TRAIL',
  USER_EXTERNAL_INVITATION: 'USER_EXTERNAL_INVITATION',
  USER_HIERARCHY: 'USER_HIERARCHY',
  USER_LOGIN_LOG: 'USER_LOGIN_LOG',
  USER_SSO: 'USER_SSO',
  USER_SUB_ACCOUNT: 'USER_SUB_ACCOUNT',
};

export const EVENT_ACTIONS = {
  ARCHIVED: 'ARCHIVED',
  ASSIGNED: 'ASSIGNED',
  CREATED: 'CREATED',
  DELETED: 'DELETED',
  DUPLICATED: 'DUPLICATED',
  EDITED: 'EDITED',
  EXPORTED: 'EXPORTED',
  GENERATED_API_KEY: 'GENERATED_API_KEY',
  IMPORTED: 'IMPORTED',
  INSTALLED: 'INSTALLED',
  IP_BLACKLISTED: 'IP_BLACKLISTED',
  KILL_SWITCH: 'KILL_SWITCH',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  RAN_TEST: 'RAN_TEST',
  RESET: 'RESET',
  SEARCHED: 'SEARCHED',
  SETUP: 'SETUP',
  UNINSTALLED: 'UNINSTALLED',
  UPDATED: 'UPDATED',
  UPDATED_RISK_SCORE: 'UPDATED_RISK_SCORE',
  UPDATED_STATUS: 'UPDATED_STATUS',
  VIEWED: 'VIEWED',
  VIEWED_DETAILS: 'VIEWED_DETAILS',
  VIEWED_HISTORY: 'VIEWED_HISTORY',
};

export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const MODULE_METHODS = {
  CREATE: 'create',
  DELETE: 'delete',
  EXPORT: 'export',
  INDEX: 'index',
  OPTION: 'option',
  UPDATE: 'update',
  UPDATE_BASIC_INFORMATION: 'updateBasicInformation',
  UPDATE_PERSONAL: 'updatePersonal',
  UPDATE_SAFETY: 'updateSafety',
  UPDATE_STATUS: 'updateStatus',
  UPDATE_THEMES: 'updateThemes',
  VIEW: 'view',
};

export const MODULE_NAMES = {
  ACCESS_CONTROL: 'accessControls',
  AUDIT_TRAIL: 'auditTrails',
  BULK_JOB: 'bulkJobs',
  CORE: 'core',
  DEVELOPER_HUB: 'developerHubs',
  LOCALISATION: 'localisations',
  SETTING: 'settings',
};

export const REMARK_STATUSES = {
  ACTIVE: 'active',
  ARCHIVED: 'archived',
};

export const REMARK_TYPE = {
  AUDIT: 'audit',
  NOTE: 'note',
  SECURITY: 'security',
  SYSTEM: 'system',
  WARNING: 'warning',
};

export const REMARKABLE_TYPE = {
  IP_ACCESS_CONTROL: 'ip_access_control',
};
