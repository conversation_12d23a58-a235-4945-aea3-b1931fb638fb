import { Typography as MuiTypography } from '@mui/material';
import * as changeCase from 'change-case';
import React from 'react';
import type { TypographyProps } from './Typography.type';

/**
 * A Material UI Typography component that can transform its children string
 * value to another case format.
 */
export const Typography = ({ children, caseTransform, ...props }: TypographyProps) => {
  // Apply the change-case transformation if specified
  let transformedChildren = children;

  if (caseTransform && typeof children === 'string') {
    // Apply the transformation
    switch (caseTransform) {
      case 'camelCase':
        transformedChildren = changeCase.camelCase(children);
        break;
      case 'capitalCase':
        transformedChildren = changeCase.capitalCase(children);
        break;
      case 'constantCase':
        transformedChildren = changeCase.constantCase(children);
        break;
      case 'dotCase':
        transformedChildren = changeCase.dotCase(children);
        break;
      case 'kebabCase':
        transformedChildren = changeCase.kebabCase(children);
        break;
      case 'lowercase':
        transformedChildren = children.toLowerCase();
        break;
      case 'noCase':
        transformedChildren = changeCase.noCase(children);
        break;
      case 'none':
        transformedChildren = children;
        break;
      case 'pascalCase':
        transformedChildren = changeCase.pascalCase(children);
        break;
      case 'pascalSnakeCase':
        transformedChildren = changeCase.pascalSnakeCase(children);
        break;
      case 'pathCase':
        transformedChildren = changeCase.pathCase(children);
        break;
      case 'sentenceCase':
        transformedChildren = changeCase.sentenceCase(children);
        break;
      case 'snakeCase':
        transformedChildren = changeCase.snakeCase(children);
        break;
      case 'trainCase':
        transformedChildren = changeCase.trainCase(children);
        break;
      case 'uppercase':
        transformedChildren = children.toUpperCase();
        break;
      default:
        transformedChildren = children;
        break;
    }
  }

  return <MuiTypography {...props}>{transformedChildren}</MuiTypography>;
};

export default Typography;
