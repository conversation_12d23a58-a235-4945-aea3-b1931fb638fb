import { CoreSchema } from '#src/modules/core/schemas/index.js';
import fp from 'fastify-plugin';

/**
 * onRoute hook
 *
 * This hook is executed when a new route is registered.
 * It can be used for logging or modifying route options.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onRouteHook = (fastify, options) => {
  fastify.addHook('onRoute', async (routeOptions) => {
    fastify.log.debug('Executing onRoute hook');

    routeOptions.schema.headers = {
      type: 'object',
      properties: {
        ...CoreSchema.COMMON_HEADER_PROPERTIES,
      },
    };
  });
};

export default fp(onRouteHook, {
  name: 'onRouteHook',
});
