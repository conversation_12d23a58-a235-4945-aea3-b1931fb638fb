import React from 'react';
import { AuthProvider } from '../src/contexts/auth/auth.context';

/**
 * Decorator that wraps components with AuthProvider
 * This is necessary for components that use the useAuth hook
 */
export const withAuthProvider = (Story: React.ComponentType) => (
  <AuthProvider>
    <Story />
  </AuthProvider>
);

/**
 * Mock implementation of GuestGuard for Storybook
 * This simply renders children without any authentication checks
 */
export const MockGuestGuard = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

/**
 * Mock implementation of useRouter for Storybook
 */
export const mockRouter = {
  push: () => {},
  replace: () => {},
  refresh: () => {},
  back: () => {},
  forward: () => {},
  prefetch: () => Promise.resolve(),
  pathname: '/',
  query: {},
};
