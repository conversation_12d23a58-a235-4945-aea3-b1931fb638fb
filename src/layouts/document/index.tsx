'use client';

import { Toaster } from '@/components/base/feedback/toaster';
import { RtlDirection } from '@/components/base/layouts/rtl-direction';
import { AuthProvider } from '@/contexts/auth/auth.context';
import {
  CustomizationConsumer,
  CustomizationProvider,
  type Customization,
} from '@/contexts/customization';
import { SidebarProvider } from '@/contexts/sidebar';
import { store } from '@/store';
import { createTheme } from '@/theme';
import { Box } from '@mui/material';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from '@mui/material/styles';
import { AdapterDateFns } from '@mui/x-date-pickers-pro/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers-pro/LocalizationProvider';
import { LicenseInfo } from '@mui/x-license';
import Head from 'next/head';
import { type ReactNode } from 'react';
import { Provider as ReduxProvider } from 'react-redux';
import { NextAppDirEmotionCacheProvider } from 'tss-react/next/appDir';
import '@/i18n/i18n';
import '@/global.css';
import { resetCustomization, updateCustomization } from '@/utils/client-side-customization.util';
import { config } from '@/utils/config.util';

LicenseInfo.setLicenseKey(config.mui.licenseKey);

interface LayoutProps {
  children: ReactNode;
  customization?: Customization;
}

export const DocumentLayout = ({ children, customization }: LayoutProps) => {
  return (
    <NextAppDirEmotionCacheProvider options={{ key: 'uifort' }}>
      <ReduxProvider store={store}>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <AuthProvider>
            <SidebarProvider>
              <CustomizationProvider
                onReset={resetCustomization}
                onUpdate={updateCustomization}
                settings={customization}
              >
                <CustomizationConsumer>
                  {(settings) => {
                    const theme = createTheme({
                      colorPreset: settings.colorPreset,
                      direction: settings.direction,
                      paletteMode: settings.paletteMode,
                      layout: settings.layout,
                    });

                    return (
                      <ThemeProvider theme={theme}>
                        <Head>
                          <meta
                            name="color-scheme"
                            content={settings.paletteMode}
                          />
                          <meta
                            name="theme-color"
                            content={theme.palette.primary.main}
                          />
                        </Head>
                        <RtlDirection direction={settings.direction}>
                          <CssBaseline />
                          <Box
                            display="flex"
                            minHeight="100vh"
                          >
                            {children}
                          </Box>
                          <Toaster />
                        </RtlDirection>
                      </ThemeProvider>
                    );
                  }}
                </CustomizationConsumer>
              </CustomizationProvider>
            </SidebarProvider>
          </AuthProvider>
        </LocalizationProvider>
      </ReduxProvider>
    </NextAppDirEmotionCacheProvider>
  );
};

export default DocumentLayout;
