import { z as zod } from 'zod';

/**
 * Login form validation schema
 *
 * Validates email and password fields for the login form.
 */
export const loginSchema = zod.object({
  email: zod.string().min(1, { message: 'Email is required' }).email(),
  password: zod.string().min(1, { message: 'Password is required' }),
});

/**
 * Login form values type
 */
export type LoginFormValues = zod.infer<typeof loginSchema>;

/**
 * Default values for the login form
 */
export const defaultLoginValues: LoginFormValues = {
  email: '',
  password: '',
};
