import { beforeEach, describe, expect, it, vi } from 'vitest';
import instance from '#src/utils/success-message.util.js'; // Import the instance directly

// Mock the CoreConstant module to match your actual constants
vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    MODULE_METHODS: {
      INDEX: 'INDEX',
      VIEW: 'VIEW',
      CREATE: 'CREATE',
      EXPORT: 'EXPORT',
      INFO: 'INFO',
      OPTION: 'OPTION',
      UPDATE: 'UPDATE',
      UPDATE_STATUS: 'UPDATE_STATUS',
      UPDATE_BASIC_INFORMATION: 'UPDATE_BASIC_INFORMATION',
      UPDATE_PERSONAL: 'UPDATE_PERSONAL',
      UPDATE_SAFETY: 'UPDATE_SAFETY',
      UPDATE_THEMES: 'UPDATE_THEMES',
      UPDATE_PERMISSION: 'UPDATE_PERMISSION',
      UPDATE_ACCESS_CONTROL: 'UPDATE_ACCESS_CONTROL',
    },
  },
}));

describe('SuccessMessage Utility', () => {
  let SuccessMessage;

  beforeEach(async () => {
    // Clear the require cache and re-import to get fresh instance
    vi.resetModules();
    const module = await import('#src/utils/success-message.util.js');
    SuccessMessage = module.default.constructor;
  });

  describe('Singleton Pattern', () => {
    it('should create only one instance', () => {
      const instance1 = SuccessMessage.getInstance();
      const instance2 = SuccessMessage.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize with message templates', () => {
      expect(instance.templates).toBeDefined();
      expect(typeof instance.templates.INDEX).toBe('function');
    });
  });

  describe('getTemplate()', () => {
    const testCases = [
      { type: 'INDEX', expected: 'Successfully retrieved user' },
      { type: 'VIEW', expected: 'Successfully retrieved a user' },
      { type: 'CREATE', expected: 'Successfully created user' },
      { type: 'EXPORT', expected: 'Successfully exported user' },
      { type: 'INFO', expected: 'Successfully retrieved user info' },
      { type: 'OPTION', expected: 'Successfully retrieved user options' },
      { type: 'UPDATE', expected: 'Successfully updated user' },
      { type: 'UPDATE_STATUS', expected: 'Successfully updated user status' },
      {
        type: 'UPDATE_BASIC_INFORMATION',
        expected: 'Successfully updated user basic information',
      },
      { type: 'UPDATE_PERSONAL', expected: 'Successfully updated user personal' },
      { type: 'UPDATE_SAFETY', expected: 'Successfully updated user safety' },
      { type: 'UPDATE_THEMES', expected: 'Successfully updated user themes' },
      { type: 'UPDATE_PERMISSION', expected: 'Successfully updated user permissions' },
      { type: 'UPDATE_ACCESS_CONTROL', expected: 'Successfully updated user access controls' },
    ];

    testCases.forEach(({ type, expected }) => {
      it(`should return correct message for ${type} operation`, () => {
        const result = instance.getTemplate('user', type);
        expect(result).toBe(expected);
      });
    });

    it('should handle camelCase module names', () => {
      expect(instance.getTemplate('userProfile', 'VIEW')).toBe(
        'Successfully retrieved a user profile',
      );
      expect(instance.getTemplate('orderHistory', 'INDEX')).toBe(
        'Successfully retrieved order history',
      );
    });

    it('should handle all uppercase module names', () => {
      expect(instance.getTemplate('accessControls', 'VIEW')).toBe(
        'Successfully retrieved a access controls',
      );
    });

    // it('should return default message for unknown operation type', () => {
    //   expect(instance.getTemplate('User', 'unknownType')).toBe('Operation successful');
    // });

    // it('should handle empty module name', () => {
    //   expect(instance.getTemplate('', 'VIEW')).toBe('Successfully retrieved a ');
    // });

    // it('should handle undefined module name', () => {
    //   expect(instance.getTemplate(undefined, 'VIEW')).toBe('Successfully retrieved a undefined');
    // });
  });

  // describe('Module Name Transformation', () => {
  //   const transformationCases = [
  //     { input: 'User', output: 'user' },
  //     { input: 'UserProfile', output: 'user profile' },
  //     { input: 'OrderHistory', output: 'order history' },
  //     { input: 'ABCModule', output: 'a b c module' },
  //     { input: 'APIEndpoint', output: 'a p i endpoint' },
  //     { input: '', output: '' },
  //   ];

  //   transformationCases.forEach(({ input, output }) => {
  //     it(`should transform "${input}" to "${output}"`, () => {
  //       const result = instance.getTemplate(input, 'VIEW');
  //       expect(result).toBe(`Successfully retrieved a ${output}`);
  //     });
  //   });
  // });
});
