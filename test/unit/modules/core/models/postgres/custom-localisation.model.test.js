import { DataTypes, Sequelize } from 'sequelize';
import { beforeAll, describe, expect, it, vi } from 'vitest';

import { COMMON_STATUSES } from '#src/modules/core/constants/core.constant.js';
import CustomLocalisationModel from '#src/modules/core/models/postgres/custom-localisation.model.js';

// Mock CoreHelper
vi.mock('#src/modules/core/helpers/index.js', () => ({
  CoreHelper: {
    checkAndUpdateVersion: vi.fn(),
  },
}));

// Mock mixins
vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  const mockApplyVersioning = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    versionedMixin: {
      applyVersioning: mockApplyVersioning,
    },
    __mocks: {
      mockApplyAuditFields,
      mockApplyVersioning,
    },
  };
});

describe('CustomLocalisation Model', () => {
  let CustomLocalisation;
  let sequelize;
  let MockLocalisation;

  beforeAll(() => {
    sequelize = new Sequelize('sqlite::memory:', { logging: false });
    const mockFastify = { psql: { connection: sequelize } };

    // Mock the Localisation model
    MockLocalisation = sequelize.define('Localisation', {
      category: DataTypes.STRING,
      name: DataTypes.STRING,
      code: DataTypes.STRING,
      metadata: DataTypes.JSON,
    });

    // Create the CustomLocalisation model
    CustomLocalisation = CustomLocalisationModel(mockFastify);

    // Mock the associate method
    CustomLocalisation.associate({ Localisation: MockLocalisation });
  });

  it('should initialize CustomLocalisation model', () => {
    expect(CustomLocalisation).toBeDefined();
    expect(CustomLocalisation.tableName).toBe('custom_localisations');
  });

  it('should have correct attributes', () => {
    const attributes = CustomLocalisation.rawAttributes;
    expect(attributes.id).toBeDefined();
    expect(attributes.parentId).toBeDefined();
    expect(attributes.entityId).toBeDefined();
    expect(attributes.status).toBeDefined();
    expect(attributes.exchangeRate).toBeDefined();
    expect(attributes.version).toBeDefined();
    expect(attributes.createdBy).toBeDefined();
    expect(attributes.updatedBy).toBeDefined();
  });

  it('should have correct virtual fields', () => {
    const virtualFields = ['category', 'name', 'code', 'metadata'];
    virtualFields.forEach((field) => {
      expect(CustomLocalisation.rawAttributes[field]).toBeDefined();
      expect(CustomLocalisation.rawAttributes[field].type instanceof DataTypes.VIRTUAL).toBe(true);
    });
  });

  it('should have correct status enum values', () => {
    const statusValues = CustomLocalisation.rawAttributes.status.values;
    expect(statusValues).toEqual(Object.values(COMMON_STATUSES));
  });

  it('should have correct associations', () => {
    const associations = CustomLocalisation.associations;
    expect(associations.localisation).toBeDefined();
    expect(associations.localisation.associationType).toBe('BelongsTo');
    expect(associations.localisation.target).toBe(MockLocalisation);
  });

  it('should have correct indexes', () => {
    const indexes = CustomLocalisation.options.indexes;
    expect(indexes).toHaveLength(1);
    expect(indexes[0].unique).toBe(true);
    expect(indexes[0].fields).toEqual(['parent_id', 'entity_id']);
  });

  it('should have auditable and versioned mixins available', async () => {
    const { auditableMixin, versionedMixin, __mocks } = await import('#src/mixins/index.js');

    expect(auditableMixin.applyAuditFields).toBeDefined();
    expect(versionedMixin.applyVersioning).toBeDefined();

    // Manually call the mixin functions
    auditableMixin.applyAuditFields(CustomLocalisation);
    versionedMixin.applyVersioning(CustomLocalisation);

    expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(CustomLocalisation);
    expect(__mocks.mockApplyVersioning).toHaveBeenCalledWith(CustomLocalisation);
  });

  it('should correctly get virtual fields "category", "name", "code", and "metadata"', async () => {
    // Create a mock localisation instance
    const mockLocalisation = {
      category: 'test_category',
      name: 'Test Name',
      code: 'TEST_CODE',
      metadata: { key: 'value' },
    };

    // Create an instance of CustomLocalisation
    const customLocalisation = CustomLocalisation.build(
      {},
      {
        include: [
          {
            association: 'localisation',
            raw: true,
          },
        ],
      },
    );

    // Manually set the localisation property
    customLocalisation.localisation = mockLocalisation;

    // Test the virtual getters
    expect(customLocalisation.category).toBe('test_category');
    expect(customLocalisation.name).toBe('Test Name');
    expect(customLocalisation.code).toBe('TEST_CODE');
    expect(customLocalisation.metadata).toEqual({ key: 'value' });
  });
});
