import { MongoClient } from "mongodb";
import { VALIDATIONS, INDEXES } from "./constant.js";

/**
 * Processes a log string by parsing it into an object, converting the timestamp to a Date object,
 * and organizing it with a year_month key for database storage.
 * 
 * @param {string} logString - JSON string containing audit trail data to be processed
 * @returns {Object|boolean} - Returns an object with 'key' (in format 'YYYY_MM') and 'value' (the parsed audit trail)
 *                             or false if processing fails
 */
export const processLog = (logString) => {
  try {
    // parse log string to object
    let auditTrail = JSON.parse(logString);

    // parse timestamp to date object
    const logDate = new Date(auditTrail.timestamp);
    auditTrail = {
      ...auditTrail,
      timestamp: logDate,
    };

    const year = logDate.getFullYear();
    const month = (logDate.getMonth() + 1).toString().padStart(2, "0");

    return {
      key: `${year}_${month}`,
      value: auditTrail,
    };
  } catch (error) {
    console.log(`Error processing incoming log. S: ${JSON.stringify(logString)}`, error);
    return false;
  }
}

/**
 * Saves audit trail logs to MongoDB collections organized by year and month.
 * Creates new collections with appropriate validations and indexes if they don't exist.
 * Implements retry logic with exponential backoff for handling transient failures.
 * 
 * @param {Object} auditTrailsByMonth - Object with keys as year_month strings and values as arrays of audit trail objects
 * @param {MongoClient} client - MongoDB client instance used to connect to the database
 * @param {Object} options - Optional configuration parameters
 * @param {number} [options.retryAttempt] - Number of retry attempts if insertion fails (defaults to env FAIL_RETRY or 5)
 * @param {string} [options.dbName] - Name of the database to use (defaults to env MONGO_DB)
 * @returns {Promise<void>} - Resolves when logs are successfully saved, rejects if all retry attempts fail
 * @throws {Error} Throws an error if saving logs fails after all retry attempts
 */
export const saveLog = async (auditTrailsByMonth, client, options = {}) => {
  // Retry when fail to insert.
  let success = false;
  let backoffMs = 1000;

  const retryAttempt = Number(options.retryAttempt || (process.env.FAIL_RETRY ?? 5));
  const dbName = options.dbName || process.env.MONGO_DB;
  for (let i = 0; i < retryAttempt; i++) {
    try {
      const database = client.db(dbName);

      for (const [yearMonth, auditTrails] of Object.entries(
        auditTrailsByMonth
      )) {
        const collectionName = `audit_trails_${yearMonth}`;
        const isCollectionExist =
          (await database.listCollections({ name: collectionName }).toArray())
            .length > 0;

        if (!isCollectionExist) {
          // create new collection
          await database.createCollection(collectionName, VALIDATIONS);

          // enable sharding
          const adminDatabase = client.db("admin");
          await adminDatabase.command({
            shardCollection: `${dbName}.${collectionName}`,
            key: { entityAccessId: "hashed" },
          });

          // initialise index
          await database.collection(collectionName).createIndexes(INDEXES);
        }

        // bulk upsert to handle idempotent
        const operations = auditTrails.map(auditTrail => ({
          updateOne: {
            filter: {
              "details.request.requestId": auditTrail.details.request.requestId,
            },
            update: { $set: auditTrail },
            upsert: true,
          },
        }));

        const collection = database.collection(collectionName);
        await collection.bulkWrite(operations);
      }

      success = true;
      break;
    } catch (error) {
      console.log(`Error inserting auditLog. T: ${i}. S: ${JSON.stringify(auditTrailsByMonth)}`, error);

      // exponential backoff
      await new Promise((resolve) => setTimeout(resolve, backoffMs));
      backoffMs *= 2;
    }
  }

  if (!success) {
    // TODO: send notification. pending notification TSD

    // Throw error so that kafka offset will not committed
    throw new Error("Failed to process log after retry");
  }
}