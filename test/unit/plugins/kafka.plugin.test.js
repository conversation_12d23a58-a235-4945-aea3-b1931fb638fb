import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import { Kafka } from 'kafkajs';
import kafkaPlugin from '#src/plugins/kafka.plugin.js';

vi.mock('kafkajs', () => ({
  Kafka: vi.fn().mockImplementation(() => ({
    producer: vi.fn(() => ({
      connect: vi.fn().mockResolvedValue(),
      send: vi.fn().mockResolvedValue(),
    })),
    admin: vi.fn(() => ({
      listTopics: vi.fn().mockResolvedValue(['existing-topic']),
      createTopics: vi.fn().mockResolvedValue(),
    })),
  })),
}));

describe('Test case for Kafka Plugin', () => {
  let fastify;

  beforeEach(() => {
    fastify = Fastify();

    fastify.log = {
      warn: vi.fn(),
      info: vi.fn(),
      error: vi.fn(),
    };

    fastify.decorate('config', {
      KAFKA: true,
      KAFKA_BROKERS: 'localhost:9092',
    });
    fastify.register(kafkaPlugin);
  });

  afterEach(() => {
    fastify.close();
  });

  it('should initialize Kafka producer and admin client', async () => {
    const mockProducer = {
      connect: vi.fn().mockResolvedValue(),
    };
    const mockAdmin = {
      listTopics: vi.fn().mockResolvedValue(['existing-topic']),
      createTopics: vi.fn().mockResolvedValue(),
    };

    Kafka.mockImplementationOnce(() => ({
      producer: vi.fn().mockReturnValue(mockProducer),
      admin: vi.fn().mockReturnValue(mockAdmin),
    }));

    await fastify.ready();

    // Check that Kafka producer and admin clients were correctly created
    expect(Kafka).toHaveBeenCalledWith({
      brokers: ['localhost:9092'],
    });
    expect(mockProducer.connect).toHaveBeenCalled();

    // Verify that the kafka decorator is added to fastify
    expect(fastify.hasDecorator('kafka')).toBe(true);
    expect(fastify.kafka).toHaveProperty('producer');
    expect(fastify.kafka).toHaveProperty('admin');
  });

  it('should not re-register kafka decorator if already registered', async () => {
    const mockProducer = {
      connect: vi.fn().mockResolvedValue(),
    };
    const mockAdmin = {
      listTopics: vi.fn().mockResolvedValue(['existing-topic']),
      createTopics: vi.fn().mockResolvedValue(),
    };

    Kafka.mockImplementationOnce(() => ({
      producer: vi.fn().mockReturnValue(mockProducer),
      admin: vi.fn().mockReturnValue(mockAdmin),
    }));

    await fastify.ready();

    const secondMockProducer = {
      connect: vi.fn().mockResolvedValue(),
    };
    const secondMockAdmin = {
      listTopics: vi.fn().mockResolvedValue(['existing-topic']),
      createTopics: vi.fn().mockResolvedValue(),
    };

    Kafka.mockImplementationOnce(() => ({
      producer: vi.fn().mockReturnValue(secondMockProducer),
      admin: vi.fn().mockReturnValue(secondMockAdmin),
    }));

    // Mock Kafka plugin registration again
    const secondFastify = Fastify();
    secondFastify.decorate('config', {
      KAFKA: true,
      KAFKA_BROKERS: 'localhost:9092',
    });
    await secondFastify.register(kafkaPlugin);

    // Ensure that the decorator is not re-registered
    await secondFastify.ready();
    expect(secondFastify.hasDecorator('kafka')).toBe(true);
  });

  it('should warn and skip initialization if Kafka config is missing', async () => {
    fastify.config.KAFKA = false;

    await fastify.register(kafkaPlugin);

    await fastify.ready();

    expect(fastify.log.warn).toHaveBeenCalledWith('Kafka configuration is missing.');
    expect(fastify.hasDecorator('kafka')).toBe(false);
  });

  it('should log error if producer.connect() fails', async () => {
    const fastifyWithError = Fastify();

    // Spy on log.error
    fastifyWithError.log = {
      warn: vi.fn(),
      info: vi.fn(),
      error: vi.fn(),
    };

    fastifyWithError.decorate('config', {
      KAFKA: true,
      KAFKA_BROKERS: 'localhost:9092',
    });

    const mockProducer = {
      connect: vi.fn().mockRejectedValue(new Error('Connection failed')),
    };
    const mockAdmin = {
      listTopics: vi.fn(),
      createTopics: vi.fn(),
    };

    Kafka.mockImplementationOnce(() => ({
      producer: () => mockProducer,
      admin: () => mockAdmin,
    }));

    await fastifyWithError.register(kafkaPlugin);
    await fastifyWithError.ready();

    expect(fastifyWithError.log.error).toHaveBeenCalled();
    expect(fastifyWithError.log.error.mock.calls[0][0]).toBeInstanceOf(Error);
    expect(fastifyWithError.log.error.mock.calls[0][1]).toBe('Error initializing Kafka');
  });
});
