import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import { CoreSchema } from '#src/modules/core/schemas/index.js';
import onRouteHook from '#src/hooks/onRoute.hook.js';

describe('Test case for onRoute hook', () => {
  let fastify;
  let onRouteFunction;

  beforeEach(async () => {
    fastify = Fastify();

    // Add spy to addHook method
    vi.spyOn(fastify, 'addHook');

    // Mock log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register hook plugin
    await fastify.register(onRouteHook, {});

    // Get the onRoute function that was registered
    onRouteFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onRoute')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onRoute hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onRoute', expect.any(Function));
  });

  it('should call log.debug when schema is provided', async () => {
    const routeOptions = { schema: {} };

    await onRouteFunction(routeOptions);

    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onRoute hook');
  });

  it('should set schema.headers with COMMON_HEADER_PROPERTIES if schema exists', async () => {
    const routeOptions = {
      schema: {},
    };

    await onRouteFunction(routeOptions);

    expect(routeOptions.schema.headers).toEqual({
      type: 'object',
      properties: {
        ...CoreSchema.COMMON_HEADER_PROPERTIES,
      },
    });
  });
});
