import transformText from '@/utils/text-transform.util';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import * as DataGridUtil from './DataGrid.util';

vi.mock('@/utils/text-transform.util');

const mockDate = new Date('2025-01-01T12:34:56.789Z');
const originalDate = global.Date;

describe('DataGrid', () => {
  describe('formatDateTime', () => {
    beforeEach(() => {
      vi.clearAllMocks();

      global.Date = vi.fn(() => mockDate) as unknown as typeof Date;
    });

    afterEach(() => {
      global.Date = originalDate;
    });

    it('should format the date to BO standard format', () => {
      const result = DataGridUtil.formatDateTime();

      expect(result).toBe('2025-01-01_12-34-56');
    });
  });

  describe('getFormattedFileName', () => {
    const datetime = '2025-01-01_12-34-56';

    beforeEach(() => {
      vi.clearAllMocks();

      global.Date = vi.fn(() => mockDate) as unknown as typeof Date;
    });

    afterEach(() => {
      global.Date = originalDate;
    });

    it('should return filename with filename_suffix_date format', () => {
      const baseFileName = 'report';
      const suffix = 'user';

      const result = DataGridUtil.getFormattedFileName(baseFileName, suffix);

      expect(result).toBe(`${baseFileName}_${suffix}_${datetime}`);
    });

    it('should return filename without suffix if not supplied', () => {
      const baseFileName = 'report';

      const result = DataGridUtil.getFormattedFileName(baseFileName);

      expect(result).toBe(`${baseFileName}_${datetime}`);
    });
  });

  describe('createHeaderName', () => {
    const fieldName = 'common.label.User';
    const mockResult = 'Transformed text';
    let mockT: (key: string) => string;

    beforeEach(() => {
      vi.clearAllMocks();

      mockT = vi.fn((key) => `Translated ${key}`);

      vi.mocked(transformText).mockReturnValue(mockResult);
    });

    it('should transform field name with translation and specified case', () => {
      const transform = 'uppercase';

      const result = DataGridUtil.createHeaderName(fieldName, mockT, transform);

      expect(mockT).toHaveBeenCalledWith(fieldName);
      expect(transformText).toHaveBeenCalledWith(mockT(fieldName), transform);
      expect(result).toEqual(mockResult);
    });

    it('should use default sentenceCase when transform is not supplied', () => {
      const transform = 'sentenceCase';

      const result = DataGridUtil.createHeaderName(fieldName, mockT);

      expect(mockT).toHaveBeenCalledWith(fieldName);
      expect(transformText).toHaveBeenCalledWith(mockT(fieldName), transform);
      expect(result).toEqual(mockResult);
    });
  });

  describe('useLocaleText', () => {
    const mockT: (key: string) => string = vi.fn();

    beforeEach(() => {
      vi.clearAllMocks();

      vi.mocked(mockT).mockImplementation((key: string) => {
        const translations: Record<string, string> = {
          'datagrid.pagination.around': 'around',
          'datagrid.pagination.moreThan': 'more than',
          'datagrid.pagination.of': 'of',
        };
        return translations[key] ?? `translated-${key}`;
      });
    });

    it('should return an object with translated strings', () => {
      const result = DataGridUtil.useLocaleText(mockT);

      expect(result.columnsManagementShowHideAllText).toBe(
        'translated-datagrid.columnsManagement.showHideAll'
      );
      expect(mockT).toHaveBeenCalledWith('datagrid.columnsManagement.showHideAll');
    });

    it('should format non-estimated pagination correctly', () => {
      const result = DataGridUtil.useLocaleText(mockT);
      const formattedText = result.paginationDisplayedRows({
        from: 1,
        to: 10,
        count: 100,
        estimated: undefined,
      });

      expect(formattedText).toBe('1–10 of 100');
      expect(mockT).toHaveBeenCalledWith('datagrid.pagination.of');
    });

    it('should handle unknown count with non-estimated pagination', () => {
      const result = DataGridUtil.useLocaleText(mockT);
      const formattedText = result.paginationDisplayedRows({
        from: 1,
        to: 10,
        count: -1,
        estimated: undefined,
      });

      expect(formattedText).toBe('1–10 of more than 10');
      expect(mockT).toHaveBeenCalledWith('datagrid.pagination.moreThan');
      expect(mockT).toHaveBeenCalledWith('datagrid.pagination.of');
    });

    it('should format estimated pagination correctly when estimated > to', () => {
      const result = DataGridUtil.useLocaleText(mockT);
      const formattedText = result.paginationDisplayedRows({
        from: 1,
        to: 10,
        count: -1,
        estimated: 50,
      });

      expect(formattedText).toBe('1–10 of around 50');
      expect(mockT).toHaveBeenCalledWith('datagrid.pagination.around');
      expect(mockT).toHaveBeenCalledWith('datagrid.pagination.of');
    });

    it('should format estimated pagination correctly when estimated <= to', () => {
      const result = DataGridUtil.useLocaleText(mockT);
      const formattedText = result.paginationDisplayedRows({
        from: 1,
        to: 10,
        count: -1,
        estimated: 5,
      });

      expect(formattedText).toBe('1–10 of more than 10');
      expect(mockT).toHaveBeenCalledWith('datagrid.pagination.moreThan');
      expect(mockT).toHaveBeenCalledWith('datagrid.pagination.of');
    });

    it('should use count when available even with estimated pagination', () => {
      const result = DataGridUtil.useLocaleText(mockT);
      const formattedText = result.paginationDisplayedRows({
        from: 1,
        to: 10,
        count: 100,
        estimated: 50,
      });

      expect(formattedText).toBe('1–10 of 100');
      expect(mockT).toHaveBeenCalledWith('datagrid.pagination.of');
    });
  });
});
