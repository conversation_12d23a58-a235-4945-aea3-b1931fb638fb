import CustomColumnButton from '@/components/base/data-display/datagrid/variants/custom-column-button/CustomColumnButton';
import CustomDensityButton from '@/components/base/data-display/datagrid/variants/custom-density-button/CustomDensityButton';
import CustomExportButton from '@/components/base/data-display/datagrid/variants/custom-export-button/CustomExportButton';
import CustomFilterButton from '@/components/base/data-display/datagrid/variants/custom-filter-button/CustomFilterButton';
import CustomQuickFilter from '@/components/base/data-display/datagrid/variants/custom-quick-filter/CustomQuickFilter';
import { Box } from '@mui/material';
import { Toolbar, type GridToolbarQuickFilterProps } from '@mui/x-data-grid-premium';
import { type CustomGridToolbarProps } from '../DataGrid.type';

/**
 * A customizable toolbar component for DataGridPremium that provides filtering,
 * column management, density selection, and export capabilities.
 */
export const CustomGridToolbar = ({
  showQuickFilter = false,
  showDensityButton = false,
  showColumnsButton = false,
  showFilterButton = false,
  showExportButton = false,
  ...props
}: CustomGridToolbarProps) => {
  const { csvOptions, quickFilterProps } = props;

  const defaultQuickFilterProps: GridToolbarQuickFilterProps = {
    debounceMs: 500,
    ...quickFilterProps,
  };

  return (
    <Toolbar>
      {showQuickFilter && <CustomQuickFilter quickFilterProps={defaultQuickFilterProps} />}
      <Box sx={{ flexGrow: 1 }} />
      {showColumnsButton && <CustomColumnButton />}
      {showFilterButton && <CustomFilterButton />}
      {showDensityButton && <CustomDensityButton />}
      {showExportButton && <CustomExportButton csvOptions={csvOptions!} />}
    </Toolbar>
  );
};

export default CustomGridToolbar;
