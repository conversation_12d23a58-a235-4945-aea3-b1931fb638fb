import { getByXPath } from '@/utils/xpath-selector.util';
import { DataGridPremium, Toolbar, type GridPremiumSlotsComponent } from '@mui/x-data-grid-premium';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, screen, waitFor } from 'storybook/test';
import CustomColumnButton from './CustomColumnButton';

/**
 * Storybook metadata configuration for the CustomColumnButton component.
 */
const meta = {
  component: CustomColumnButton,
  title: 'Components/base/data-display/datagrid/CustomColumnButton',
  tags: ['autodocs'],
  decorators: [
    (Story) => {
      const CustomToolbarComponent = () => (
        <Toolbar>
          <Story />
        </Toolbar>
      );

      const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
        toolbar: CustomToolbarComponent,
      };
      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={defaultSlotProps}
        />
      );
    },
  ],
} satisfies Meta<typeof CustomColumnButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomColumnButton component.
 */
export const Default: Story = {
  play: async ({ canvas, userEvent, step }) => {
    await step('Able to show tooltip', async () => {
      await userEvent.hover(canvas.getByText('common.button.columns'));

      const tooltip = await waitFor(() =>
        getByXPath(
          `//div[@role="tooltip" and contains(@class, "MuiTooltip-popper") and contains(., "common.label.selectColumns")]`,
          document
        )
      );

      expect(tooltip).toBeInTheDocument();
    });

    await step('Click columns button', async () => {
      await userEvent.click(canvas.getByText('common.button.columns'));
    });

    await step('Menu is shown when clicked', async () => {
      const menu = await waitFor(() => screen.getByPlaceholderText('Search'));
      expect(menu).toBeInTheDocument();
    });

    await step('Menu can be closed with escape button', async () => {
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(screen.queryByPlaceholderText('Search')).not.toBeInTheDocument();
      });
    });
  },
};
