import { DataGridPremium, Toolbar, type GridPremiumSlotsComponent } from '@mui/x-data-grid-premium';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, waitFor } from 'storybook/test';
import CustomQuickFilter from './CustomQuickFilter';

/**
 * Storybook metadata configuration for the CustomQuickFilter component.
 */
const meta = {
  component: CustomQuickFilter,
  title: 'Components/base/data-display/datagrid/CustomQuickFilter',
  tags: ['autodocs'],
  decorators: [
    (Story) => {
      const CustomToolbarComponent = () => (
        <Toolbar>
          <Story />
        </Toolbar>
      );

      const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
        toolbar: CustomToolbarComponent,
      };
      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={defaultSlotProps}
        />
      );
    },
  ],
} satisfies Meta<typeof CustomQuickFilter>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomQuickFilter component.
 */
export const Default: Story = {
  args: {
    quickFilterProps: {
      debounceMs: 500,
    },
  },
  play: async ({ canvas, userEvent, step }) => {
    await step('Should default expanded', async () => {
      expect(canvas.getByPlaceholderText('common.label.searchKeywords')).toBeInTheDocument();
    });

    await step('Should show clear button when typed', async () => {
      await userEvent.type(canvas.getByRole('searchbox'), 'Test');

      expect(canvas.getByTestId('CancelIcon')).toBeInTheDocument();
    });

    await step('Should clear all text with escape button', async () => {
      await userEvent.keyboard('{Escape}');
      await waitFor(() => {
        expect(canvas.queryByTestId('CancelIcon')).not.toBeInTheDocument();
      });
    });
  },
};
