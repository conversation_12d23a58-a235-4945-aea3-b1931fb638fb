/**
 * Example file showing how to use the reusable Storybook decorators
 * This file demonstrates the before/after comparison and usage patterns
 */

// ❌ BEFORE: Repetitive decorator code in each story file
const oldApproach = `
// CustomDensityButton.stories.tsx
const meta = {
  component: CustomDensityButton,
  decorators: [
    (Story) => {
      const CustomToolbarComponent = () => (
        <Toolbar>
          <Story />
        </Toolbar>
      );
      const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
        toolbar: CustomToolbarComponent,
      };
      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={defaultSlotProps}
        />
      );
    },
  ],
};

// CustomExportButton.stories.tsx - SAME CODE REPEATED!
const meta = {
  component: CustomExportButton,
  decorators: [
    (Story) => {
      const CustomToolbarComponent = () => (
        <Toolbar>
          <Story />
        </Toolbar>
      );
      const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
        toolbar: CustomToolbarComponent,
      };
      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={defaultSlotProps}
        />
      );
    },
  ],
};
`;

// ✅ AFTER: Clean, reusable decorator approach
const newApproach = `
// 1. Basic usage - replaces 20+ lines with 1 line
import { withDataGridToolbar } from '@/test-utils/storybook-decorators';

const meta = {
  component: CustomDensityButton,
  decorators: [withDataGridToolbar()],
};

// 2. With custom configuration
const meta = {
  component: CustomExportButton,
  decorators: [withDataGridToolbar({
    columns: [
      { field: 'id', headerName: 'ID' },
      { field: 'name', headerName: 'Name' },
    ],
    rows: [{ id: 1, name: 'Test' }],
  })],
};

// 3. With sample data for better visual testing
const meta = {
  component: CustomFilterButton,
  decorators: [withDataGridToolbarAndData()],
};

// 4. Story-specific decorator override
export const WithLargeDataset: Story = {
  decorators: [withDataGridToolbar({
    rows: Array.from({ length: 100 }, (_, i) => ({ 
      id: i, 
      name: \`User \${i}\` 
    })),
  })],
};
`;

// 📊 Usage patterns for different scenarios

// Pattern 1: Simple toolbar component (most common)
const simpleUsage = `
import { withDataGridToolbar } from '@/test-utils/storybook-decorators';

const meta = {
  component: CustomDensityButton,
  decorators: [withDataGridToolbar()],
};
`;

// Pattern 2: Component that needs specific data
const withDataUsage = `
import { withDataGridToolbarAndData } from '@/test-utils/storybook-decorators';

const meta = {
  component: CustomQuickFilter,
  decorators: [withDataGridToolbarAndData()],
};
`;

// Pattern 3: Export button with CSV options
const exportUsage = `
import { withDataGridExportToolbar } from '@/test-utils/storybook-decorators';

const meta = {
  component: CustomExportButton,
  decorators: [withDataGridExportToolbar({ fileName: 'test-export' })],
};
`;

// Pattern 4: Custom configuration per story
const customUsage = `
export const WithCustomData: Story = {
  decorators: [withDataGridToolbar({
    columns: [
      { field: 'id', headerName: 'ID' },
      { field: 'status', headerName: 'Status' },
    ],
    rows: [
      { id: 1, status: 'Active' },
      { id: 2, status: 'Inactive' },
    ],
    gridProps: {
      checkboxSelection: true,
      disableRowSelectionOnClick: true,
    },
  })],
};
`;

// Pattern 5: Multiple decorators
const multipleDecoratorsUsage = `
import { withDataGridToolbar, withContainer } from '@/test-utils/storybook-decorators';

const meta = {
  decorators: [
    withContainer({ style: { backgroundColor: '#f5f5f5' } }),
    withDataGridToolbar(),
  ],
};
`;

export {
  oldApproach,
  newApproach,
  simpleUsage,
  withDataUsage,
  exportUsage,
  customUsage,
  multipleDecoratorsUsage,
};
