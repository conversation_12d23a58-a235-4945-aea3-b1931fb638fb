import fp from 'fastify-plugin';

import { buildAuditTrailEntry } from '#src/utils/audit-trail.util.js';
import { bypassAccessCheck } from '#src/utils/access.util.js';
/**
 * preValidation hook
 *
 * This hook is executed after the request has been parsed and before the input validation.
 * It can be used for custom validation or data manipulation before the actual validation.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const preValidationHook = (fastify, options) => {
  fastify.addHook('preValidation', async (request, reply) => {
    fastify.log.debug('Executing preValidation hook');
    // Add your preValidation logic here

    if (!bypassAccessCheck(request)) {
      // Audit trail logic is placed here instead of onRequest, since the request body is not yet parsed in the onRequest hook.
      buildAuditTrailEntry(fastify, request);
    }
  });
};

export default fp(preValidationHook, {
  name: 'preValidationHook',
});
