import type { ThemeColor } from '@/theme/colors';
import { alpha, Avatar, styled } from '@mui/material';
import { type AvatarGradientProps } from '../../Avatar.type';

/**
 * Avatar component with gradient background and colored border
 *
 * @example
 * ```tsx
 * <AvatarGradient
 *   color="primary"
 *   size={40}
 *   alt="User Avatar"
 *   src="/path/to/avatar.jpg"
 * />
 * ```
 */
export const AvatarGradient = styled(Avatar, {
  shouldForwardProp: (prop) => prop !== 'color' && prop !== 'borderRadius' && prop !== 'size',
})<AvatarGradientProps>(({ theme, color = 'primary', borderRadius, size }) => {
  // Get the main color from the palette
  const mainColor = (theme.palette[color as keyof typeof theme.palette] as ThemeColor).main;

  // Calculate border radius based on variant and custom borderRadius
  let borderRadiusValue;
  if (borderRadius !== undefined) {
    borderRadiusValue = `${borderRadius}px`;
  } else {
    borderRadiusValue = theme.shape.borderRadius * 2 + 'px';
  }

  return {
    ...(size && { width: size, height: size }),
    background: `linear-gradient(198deg, ${alpha(mainColor, 0.32)} 18%, transparent 100%)`,
    border: `2px solid ${mainColor}`,
    borderRadius: borderRadiusValue,
  };
});

export default AvatarGradient;
