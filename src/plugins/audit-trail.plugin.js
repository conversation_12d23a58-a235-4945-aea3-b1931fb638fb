import _ from 'lodash';
import fp from 'fastify-plugin';
import { generateEvents } from '#src/utils/audit-trail.util.js';

async function auditTrailPlugin(fastify) {
  fastify.decorate('withAuditLogging', withAuditLogging);
}

/**
 * A function to handle audit logging for CRUD operations. It generates audit trail entries based on the provided parameters.
 *
 * @param {Object} options - An object containing the necessary parameters for audit logging.
 * @param {Object} options.request - The request object containing audit trail entries.
 * @param {Object} options.modelMapping - Maps model names to their associated audit information:
 *   {
 *     ModelName: {
 *       fieldsChanged: Array<string> | false,
 *       beforeState: Object,
 *       afterState: Object
 *     },
 *     ...
 *   }
 * @param {boolean} [options.isMultiple=false] - Indicates whether the operation involves multiple records.
 * @param {Object} [options.metrics={}] - Additional metrics related to the operation.
 * @param {number} [options.status=null] - The HTTP status code associated with the operation.
 *
 * @returns {Promise<Array>} - A promise that resolves to an array of target objects, each containing the model name, reference ID, and changes made.
 */
const withAuditLogging = async ({
  request,
  modelMapping = {},
  isMultiple = false,
  metrics = {},
  status = null,
}) => {
  const targets = processChanges(modelMapping, request.auditEntries.action, isMultiple);
  const referenceIds = targets.map((t) => t.referenceId).join(', ');

  const eventInfo = await generateEvents(request, referenceIds, status);

  buildAuditTrailEntry(request, {
    eventInfo,
    targets,
    metrics,
  });

  return targets;
};

/**
 * Processes changes between two objects and generates target objects based on the provided parameters.
 *
 * @param {Object|string} modelMapping - A mapping of model names to their associated audit information.
 * @param {string} action - The action performed (e.g., CREATE, UPDATE, DELETE).
 *
 * @returns {Array} - An array of target objects, each containing the model name, reference ID, and changes made.
 */
const processChanges = (modelMapping, action, isMultiple) => {
  const targets = [];
  for (const [model, data] of Object.entries(modelMapping)) {
    const { fieldsChanged, beforeState, afterState } = data;

    const beforeVal = extractData(beforeState);
    const afterVal = extractData(afterState);
    const changes = getChangedFields(beforeVal, afterVal, fieldsChanged, isMultiple);

    if (_.isEmpty(changes)) {
      continue;
    }

    const viewActions = ['VIEWED', 'VIEWED_DETAILS', 'SEARCHED'];

    if (viewActions.includes(action)) {
      const referenceId =
        beforeVal?.id || afterVal?.id || beforeVal[0]?._id || afterVal[0]?._id || 'UNKNOWN_ID';

      targets.push({
        model,
        referenceId,
      });
    } else {
      if (!isMultiple) {
        const referenceId =
          beforeVal?.id || afterVal?.id || beforeVal[0]?._id || afterVal[0]?._id || 'UNKNOWN_ID';

        targets.push({
          model,
          referenceId,
          changes: {
            beforeState: changes.beforeChanged,
            afterState: changes.afterChanged,
          },
        });
      } else {
        for (const field of Object.keys(changes.beforeChanged)) {
          const before = changes.beforeChanged[field];
          const after = changes.afterChanged[field];
          const referenceId = before?.id || after?.id || 'UNKNOWN_ID';

          targets.push({
            model,
            referenceId,
            changes: {
              beforeState: {
                [field]: before?.value,
              },
              afterState: {
                [field]: after?.value,
              },
            },
          });
        }
      }
    }
  }

  return targets;
};

const extractData = (data) => {
  if (!data) {
    return {};
  }
  if (data?.dataValues) {
    return data.dataValues;
  }
  return data;
};

const getChangedFields = (before, after, fieldsChanged, isMultiple) => {
  const beforeChanged = {};
  const afterChanged = {};
  let keys = [];

  if (fieldsChanged === false) {
    // Explicitly no changes
    keys = [];
  } else if (fieldsChanged && Array.isArray(fieldsChanged)) {
    // Use explicitly provided changed fields (e.g., for updates)
    keys = fieldsChanged;
  } else if (!after || Object.keys(after).length === 0) {
    // DELETE action – derive keys from beforeState
    keys = Object.keys(before);
  } else {
    // CREATE action – derive keys from afterState
    keys = Object.keys(after);
  }

  if (!keys) {
    return;
  }

  for (const key of keys) {
    if (!isMultiple) {
      beforeChanged[key] = before?.[key];
      afterChanged[key] = after?.[key];
    } else {
      const beforeField = before?.[key];
      const afterValue = after?.[key];
      const beforeValue = beforeField?.value ?? beforeField;

      if (beforeValue !== afterValue) {
        beforeChanged[key] = {
          id: beforeField?.id,
          value: beforeValue,
        };
        afterChanged[key] = {
          id: beforeField?.id,
          value: afterValue,
        };
      }
    }
  }

  return { beforeChanged, afterChanged };
};

const buildAuditTrailEntry = (request, { eventInfo, targets, metrics }) => {
  request.auditEntries.target = Object.values(targets).map((target) => ({
    referenceId: target.referenceId,
    model: target.model,
    changes: target.changes,
  }));
  request.auditEntries.statusCode = request.statusCode || 200;

  if (metrics && Object.keys(metrics).length > 0) {
    request.auditEntries.metrics = metrics;
  }

  request.auditEntries.description = eventInfo?.description;
};

export default fp(auditTrailPlugin, {
  name: 'auditTrail',
});
