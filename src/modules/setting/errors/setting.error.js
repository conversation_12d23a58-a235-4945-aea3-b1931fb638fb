import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const SETTING_ERROR_DEF = {
  invalidData: ['52400', '%s', 400],
  accessDenied: ['52403', 'Access denied for setting with category: %s', 403],
  notFound: ['52404', 'Setting not found with category: %s', 404],
  unsupportedInfo: ['52422', 'Unsupported info operation for category: %s', 422],
};

export const settingError = createModuleErrors(MODULE_NAMES.SETTING, SETTING_ERROR_DEF);
