import { beforeEach, describe, expect, it, vi } from 'vitest';
import { generateEvents as auditTrailGenerateEvents } from '#src/utils/audit-trail.util.js';
import auditTrailPlugin from '#src/plugins/audit-trail.plugin.js';
import fastify from 'fastify';

// Mock the generateEvents from audit-trail.util.js
vi.mock('#src/utils/audit-trail.util.js', () => ({
  generateEvents: vi.fn(),
}));

describe('Test case for AuditTrail Plugin', () => {
  let app;

  beforeEach(async () => {
    app = fastify();
    app.register(auditTrailPlugin);
    await app.ready();
    vi.clearAllMocks();
  });

  it('should decorate fastify with withAuditLogging', () => {
    expect(app.withAuditLogging).toBeDefined();
    expect(typeof app.withAuditLogging).toBe('function');
  });

  describe('withAuditLogging', () => {
    let request;
    let mockMetrics;
    const commonEventInfo = { description: 'Generated event description' };

    beforeEach(() => {
      request = {
        auditEntries: {
          action: 'DEFAULT_ACTION',
          target: [],
          metrics: {},
          description: '',
        },
        statusCode: 200,
      };
      mockMetrics = { timeTaken: 123 };

      auditTrailGenerateEvents.mockResolvedValue(commonEventInfo);
    });

    it('should generate events and build an audit trail entry for UPDATE', async () => {
      const modelMapping = {
        UserModel: {
          fieldsChanged: ['name', 'email'],
          beforeState: { id: '123', name: 'John', email: '<EMAIL>' },
          afterState: { id: '123', name: 'John Doe', email: '<EMAIL>' },
        },
      };

      request.auditEntries.action = 'UPDATE';

      const expectedTargets = [
        {
          referenceId: '123',
          model: 'UserModel',
          changes: {
            beforeState: { name: 'John', email: '<EMAIL>' },
            afterState: { name: 'John Doe', email: '<EMAIL>' },
          },
        },
      ];

      const result = await app.withAuditLogging({
        request,
        modelMapping,
        metrics: mockMetrics,
        status: 200,
      });

      // Validate that generateEvents was called with correct arguments
      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        '123', // referenceIds string from targets
        200,
      );

      // Validate the return value of withAuditLogging
      expect(result).toEqual(expectedTargets);

      // Validate that the audit trail entry was built correctly in request.auditEntries
      expect(request.auditEntries.target).toEqual(expectedTargets);
      expect(request.auditEntries.metrics).toEqual(mockMetrics);
      expect(request.auditEntries.description).toBe(commonEventInfo.description);
      expect(request.auditEntries.statusCode).toBe(200);
    });

    it('should handle VIEWED action and include referenceId only', async () => {
      const modelMapping = {
        UserModel: {
          fieldsChanged: false,
          beforeState: { id: '123' },
          afterState: { id: '123' },
        },
      };

      request.auditEntries.action = 'VIEWED';
      const expectedTargets = [{ model: 'UserModel', referenceId: '123' }];

      const result = await app.withAuditLogging({
        request,
        modelMapping,
        metrics: mockMetrics,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        '123', // referenceIds string
        null, // status, as not provided in options
      );

      expect(result).toEqual(expectedTargets);

      // Ensure that no changes are added to the target in this case
      expect(request.auditEntries.target).toEqual(expectedTargets);
      expect(request.auditEntries.target[0].changes).toBeUndefined();
    });

    it('should handle multiple models and generate correct audit entries', async () => {
      const modelMapping = {
        UserModel: {
          fieldsChanged: ['name'],
          beforeState: { id: '123', name: 'John' },
          afterState: { id: '123', name: 'John Doe' },
        },
        OrderModel: {
          fieldsChanged: ['status'],
          beforeState: { id: '456', status: 'pending' },
          afterState: { id: '456', status: 'completed' },
        },
      };

      request.auditEntries.action = 'UPDATE'; // Set the action for this test

      const expectedTargets = [
        {
          model: 'UserModel',
          referenceId: '123',
          changes: {
            beforeState: { name: 'John' },
            afterState: { name: 'John Doe' },
          },
        },
        {
          model: 'OrderModel',
          referenceId: '456',
          changes: {
            beforeState: { status: 'pending' },
            afterState: { status: 'completed' },
          },
        },
      ];

      const result = await app.withAuditLogging({
        request,
        modelMapping,
        metrics: mockMetrics,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(request, '123, 456', null);

      expect(result).toEqual(expectedTargets);

      // Ensure the audit trail entry reflects both models' changes
      expect(request.auditEntries.target).toEqual(expectedTargets);
      expect(request.auditEntries.metrics).toEqual(mockMetrics);
      expect(request.auditEntries.description).toBe(commonEventInfo.description);
    });

    it('should handle DELETE action and include beforeState only', async () => {
      const modelMapping = {
        UserModel: {
          // fieldsChanged should be null/undefined for DELETE to derive all keys
          beforeState: { id: '123', name: 'John Doe', email: '<EMAIL>' },
          afterState: null, // Simulate DELETE
        },
      };

      request.auditEntries.action = 'DELETE'; // Set the action for this test

      const expectedTargets = [
        {
          model: 'UserModel',
          referenceId: '123',
          changes: {
            beforeState: { id: '123', name: 'John Doe', email: '<EMAIL>' },
            afterState: {},
          },
        },
      ];

      const result = await app.withAuditLogging({
        request,
        modelMapping,
        metrics: mockMetrics,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        '123', // referenceIds
        null, // status
      );

      expect(result).toEqual(expectedTargets);
      expect(request.auditEntries.target).toEqual(expectedTargets);
    });

    it('should handle CREATE action and include afterState only', async () => {
      const modelMapping = {
        UserModel: {
          beforeState: null, // Simulate CREATE
          afterState: { id: '789', name: 'Jane Doe', email: '<EMAIL>' },
        },
      };

      request.auditEntries.action = 'CREATE'; // Set the action for this test

      const expectedTargets = [
        {
          model: 'UserModel',
          referenceId: '789',
          changes: {
            beforeState: {}, // getChangedFields returns an empty beforeChanged for CREATE
            afterState: { id: '789', name: 'Jane Doe', email: '<EMAIL>' },
          },
        },
      ];

      const result = await app.withAuditLogging({
        request,
        modelMapping,
        metrics: mockMetrics,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        '789', // referenceIds
        null, // status
      );

      expect(result).toEqual(expectedTargets);
      expect(request.auditEntries.target).toEqual(expectedTargets);
    });

    it('should use beforeState.id as referenceId when present (priority)', async () => {
      const modelMapping = {
        ModelA: {
          beforeState: { id: 'before-id-123', someField: 'old' },
          afterState: { id: 'after-id-456', someField: 'new' },
        },
      };

      request.auditEntries.action = 'UPDATE';

      const result = await app.withAuditLogging({
        request,
        modelMapping,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        'before-id-123', // Should pick beforeState.id
        null,
      );
      expect(result[0].referenceId).toBe('before-id-123');
    });

    it('should use afterState.id as referenceId when beforeState.id is missing', async () => {
      const modelMapping = {
        ModelA: {
          beforeState: null,
          afterState: { id: 'after-id-456', someField: 'new' },
        },
      };

      request.auditEntries.action = 'CREATE';

      const result = await app.withAuditLogging({
        request,
        modelMapping,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        'after-id-456', // Should pick afterState.id
        null,
      );
      expect(result[0].referenceId).toBe('after-id-456');
    });

    it('should use beforeState[0]._id as referenceId when direct ids are missing', async () => {
      const modelMapping = {
        ModelA: {
          beforeState: [{ _id: 'before-array-id-789', value: 'old' }],
          afterState: [{ _id: 'after-array-id-012', value: 'new' }],
        },
      };

      request.auditEntries.action = 'UPDATE';

      const result = await app.withAuditLogging({
        request,
        modelMapping,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        'before-array-id-789', // Should pick beforeState[0]._id
        null,
      );
      expect(result[0].referenceId).toBe('before-array-id-789');
    });

    it('should use afterState[0]._id as referenceId when other ids are missing', async () => {
      const modelMapping = {
        ModelA: {
          beforeState: null,
          afterState: [{ _id: 'after-array-id-012', value: 'new' }],
        },
      };

      request.auditEntries.action = 'CREATE';

      const result = await app.withAuditLogging({
        request,
        modelMapping,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        'after-array-id-012', // Should pick afterState[0]._id
        null,
      );
      expect(result[0].referenceId).toBe('after-array-id-012');
    });

    it('should fallback to UNKNOWN_ID when no identifiable ids are found', async () => {
      const modelMapping = {
        ModelA: {
          beforeState: { someKey: 'value' }, // No 'id' or '_id'
          afterState: { anotherKey: 'value' }, // No 'id' or '_id'
        },
      };

      request.auditEntries.action = 'UPDATE';

      const result = await app.withAuditLogging({
        request,
        modelMapping,
      });

      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        'UNKNOWN_ID', // Should fallback to UNKNOWN_ID
        null,
      );
      expect(result[0].referenceId).toBe('UNKNOWN_ID');
    });

    it('should handle isMultiple = true with different values', async () => {
      const modelMapping = {
        Item: {
          fieldsChanged: ['item-1'],
          beforeState: {
            'item-1': { id: 'item-1-id', value: 10 },
            'item-2': { id: 'item-2-id', value: 20 },
          },
          afterState: {
            'item-1': 15,
            'item-2': 20,
          },
        },
      };

      request.auditEntries.action = 'UPDATE';

      const result = await app.withAuditLogging({
        request,
        modelMapping,
        isMultiple: true,
      });

      expect(result).toEqual([
        {
          model: 'Item',
          referenceId: 'item-1-id',
          changes: {
            beforeState: { 'item-1': 10 },
            afterState: { 'item-1': 15 },
          },
        },
      ]);

      // Check if referenceIds passed to generateEvents is correct
      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(request, 'item-1-id', null);

      // Verify audit entries
      expect(request.auditEntries.target).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ model: 'Item', referenceId: 'item-1-id' }),
        ]),
      );
    });

    it('should handle isMultiple = true with unchanged values', async () => {
      const modelMapping = {
        Item: {
          fieldsChanged: [''],
          beforeState: {
            'item-1': { id: 'item-1-id', value: 10 },
            'item-2': { id: 'item-2-id', value: 20 },
          },
          afterState: {
            'item-1': 10,
            'item-2': 20,
          },
        },
      };

      request.auditEntries.action = 'UPDATE';

      const result = await app.withAuditLogging({
        request,
        modelMapping,
        isMultiple: true,
      });

      // No changes, so targets should be empty
      expect(result).toEqual([]);
      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(
        request,
        '', // Empty referenceIds string
        null,
      );
      expect(request.auditEntries.target).toEqual([]);
    });

    it('should fallback to UNKNOWN_ID when id is missing', async () => {
      const modelMapping = {
        UnknownModel: {
          fieldsChanged: ['field'],
          beforeState: { field: 'a' },
          afterState: { field: 'b' },
        },
      };

      request.auditEntries.action = 'UPDATE';

      const result = await app.withAuditLogging({
        request,
        modelMapping,
        metrics: mockMetrics,
      });

      expect(result[0].referenceId).toBe('UNKNOWN_ID');
      expect(auditTrailGenerateEvents).toHaveBeenCalledWith(request, 'UNKNOWN_ID', null);
    });

    it('should use default status code 200 when none is provided', async () => {
      const modelMapping = {
        UserModel: {
          fieldsChanged: ['name'],
          beforeState: { id: '123', name: 'Old' },
          afterState: { id: '123', name: 'New' },
        },
      };

      delete request.statusCode; // simulate no statusCode in request

      request.auditEntries.action = 'UPDATE';

      await app.withAuditLogging({
        request,
        modelMapping,
      });

      expect(request.auditEntries.statusCode).toBe(200);
    });
  });
});
