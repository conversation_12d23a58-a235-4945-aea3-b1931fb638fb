import { REMARK_STATUSES } from '#src/modules/core/constants/core.constant.js';

/**
 * Creates a new remark in the database.
 * @param {Object} fastify - The Fastify instance.
 * @param {Object} data - The data for the new remark.
 * @param {Object} [options={}] - Additional options for creating the remark.
 * @returns {Promise<Object>} A promise that resolves to the created remark.
 */
export const create = async (fastify, data, options = {}) => {
  return await fastify.psql.Remark.create(data, options);
};

/**
 * Finds the active remark for a given remarkable entity.
 * @param {Object} fastify - The Fastify instance.
 * @param {string|number} remarkableId - The ID of the remarkable entity.
 * @param {string} remarkableType - The type of the remarkable entity.
 * @param {Object} [options={}] - Additional options for the query.
 * @returns {Promise<Object|null>} A promise that resolves to the active remark or null if not found.
 */
export const findActiveByRemarkable = async (
  fastify,
  remarkableId,
  remarkableType,
  options = {},
) => {
  return await fastify.psql.Remark.findOne({
    where: {
      remarkableId,
      remarkableType,
      status: REMARK_STATUSES.ACTIVE,
    },
    ...options,
  });
};

/**
 * Archives a remark by updating its status to ARCHIVED.
 * @param {Object} remark - The remark object to be archived.
 * @param {Object} [options={}] - Additional options for updating the remark.
 * @returns {Promise<Object>} A promise that resolves to the updated remark.
 */
export const archive = async (remark, options = {}) => {
  return await remark.update({ status: REMARK_STATUSES.ARCHIVED }, options);
};
