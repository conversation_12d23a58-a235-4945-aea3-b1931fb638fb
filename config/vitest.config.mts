import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { storybookTest } from '@storybook/addon-vitest/vitest-plugin';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineConfig } from 'vitest/config';

const dirname =
  typeof __dirname !== 'undefined' ? __dirname : path.dirname(fileURLToPath(import.meta.url));

// Get the project root directory (one level up from config directory)
const projectRoot = path.resolve(dirname, '..');

export default defineConfig({
  test: {
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.next/**',
      '**/coverage/**',
      'src/**/constants/**',
      'src/**/types/**',
      'src/**/router/**',
      'src/**/styles/**',
      'src/**/mocks/**',
      'src/**/*.style.{ts,tsx}',
      'src/**/*.styles.{ts,tsx}',
      'src/**/*.mock.{ts,tsx}',
    ],
    setupFiles: ['./config/vitest.setup.ts'],
    coverage: {
      enabled: true,
      include: ['src/**/*.{ts,tsx}'], // Adjust the glob pattern if needed
      exclude: [
        'src/**/*.stories.{ts,tsx}',
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/**/constants/**',
        'src/**/types/**',
        'src/**/router/**',
        'src/**/styles/**',
        'src/**/mocks/**',
        'src/**/*.constant.ts',
        'src/**/*.constants.ts',
        'src/**/*.d.ts',
        'src/**/*.type.ts',
        'src/**/*.types.ts',
        'src/**/*.style.{ts,tsx}',
        'src/**/*.styles.{ts,tsx}',
        'src/**/*.mock.{ts,tsx}',
        'src/**/index.ts',
        'src/app/layout.tsx',
        'src/theme/**',
      ],
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      reportsDirectory: './coverage',
      // Enable the following line to enforce stricter code coverage thresholds when committing code
      // thresholds: {
      //   statements: 80,
      //   branches: 80,
      //   functions: 80,
      //   lines: 80,
      // },
    },
    testTimeout: 10000,
    workspace: [
      // Regular tests workspace
      {
        plugins: [tsconfigPaths(), react()],
        root: projectRoot,
        test: {
          name: 'unit',
          include: ['src/**/*.test.{ts,tsx}', 'src/**/*.spec.{ts,tsx}'],
          environment: 'jsdom',
          globals: true,
          typecheck: {
            enabled: true,
            tsconfig: './tsconfig.json',
          },
          deps: {
            inline: ['@mui/x-data-grid'],
          },
        },
      },
      // Storybook tests workspace
      {
        extends: true,
        root: projectRoot,
        plugins: [
          // The plugin will run tests for the stories defined in your Storybook config
          // See options at: https://storybook.js.org/docs/writing-tests/test-addon#storybooktest
          storybookTest({ configDir: path.join(projectRoot, '.storybook') }),
        ],
        test: {
          name: 'storybook',
          browser: {
            enabled: true,
            headless: true,
            provider: 'playwright',
            instances: [{ browser: 'chromium' }],
          },
          setupFiles: ['config/vitest.setup.ts'],
        },
      },
    ],
  },
});
