import * as AuditTrailRepository from '#src/modules/audit-trail/repository/audit-trail.repository.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { applyMongoFiltersCursorPagination } from '#src/utils/pagination.util.js';
import getAuditTrailModel from '#src/modules/core/models/mongo/audit-trail.model.js';

vi.mock('#src/utils/pagination.util.js', () => ({
  applyMongoFiltersCursorPagination: vi.fn(),
}));

vi.mock('#src/modules/core/models/mongo/audit-trail.model.js', () => ({
  default: vi.fn(),
}));

describe('Test case for AuditTrail Repository', () => {
  let mockRequest;
  let mockAuditTrail;

  beforeEach(() => {
    vi.clearAllMocks();

    mockRequest = {
      query: {
        filter_timestamp_gte: new Date('2025-04-01T00:00:00Z'),
        filter_timestamp_lte: new Date('2025-04-30T00:00:00Z'),
        filter_timestamp_eq: new Date('2025-04-30T00:00:00Z'),
      },
      server: {
        withAuditLogging: vi.fn(),
      },
    };
  });

  it('should call applyMongoFiltersCursorPagination in findAll', async () => {
    mockAuditTrail = { find: vi.fn().mockResolvedValue([{ id: 1 }]) };

    getAuditTrailModel.mockReturnValue(mockAuditTrail);

    applyMongoFiltersCursorPagination.mockResolvedValue('mocked result');

    const result = await AuditTrailRepository.findAll(mockRequest, {
      startDateTime: mockRequest.query.filter_timestamp_lte,
      isExport: false,
    });

    expect(getAuditTrailModel).toHaveBeenCalledWith(
      mockRequest.server,
      mockRequest.query.filter_timestamp_lte,
    );
    expect(applyMongoFiltersCursorPagination).toHaveBeenCalledWith(
      mockAuditTrail,
      mockRequest.query,
      false,
    );
    expect(result).toBe('mocked result');
  });

  it('should handle errors in findAll', async () => {
    applyMongoFiltersCursorPagination.mockRejectedValue(new Error('Some DB Error'));
    const result = await AuditTrailRepository.findAll(mockRequest, {
      startDateTime: mockRequest.query.filter_timestamp_lte,
      isExport: false,
    });

    expect(result).toEqual({ error: 'Some DB Error' });
  });

  it('should call findById and return result', async () => {
    mockAuditTrail = {
      find: vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue([{ id: 1 }]),
      }),
    };

    getAuditTrailModel.mockReturnValue(mockAuditTrail);

    const mockId = '12345';
    const result = await AuditTrailRepository.findById(mockRequest, mockId);

    expect(getAuditTrailModel).toHaveBeenCalledWith(
      mockRequest.server,
      mockRequest.query.filter_timestamp_eq,
    );
    expect(mockAuditTrail.find).toHaveBeenCalledWith({ _id: mockId });
    expect(result).toEqual([{ id: 1 }]);
  });

  it('should handle error in findById when no result is found', async () => {
    const mockAuditTrail = {
      find: vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue([]),
      }),
    };

    getAuditTrailModel.mockReturnValue(mockAuditTrail);

    const mockId = '12345';
    const result = await AuditTrailRepository.findById(mockRequest, mockId);

    expect(result).toEqual([]);
  });

  it('should handle errors in findById', async () => {
    const mockAuditTrail = {
      find: vi.fn().mockReturnValue({
        exec: vi.fn().mockRejectedValue(new Error('Some DB Error')),
      }),
    };

    getAuditTrailModel.mockReturnValue(mockAuditTrail);

    const mockId = '12345';
    const result = await AuditTrailRepository.findById(mockRequest, mockId);

    expect(result).toEqual({ error: 'Some DB Error' });
  });
});
