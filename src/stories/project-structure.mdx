import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Documentation/Architecture/Project Structure" />

# Frontend Project Structure Guidelines

This document outlines the architecture and organization patterns used in our
Next.js frontend application. Following these guidelines ensures consistency
across the codebase and makes it easier for new developers to understand and
contribute to the project.

## Directory Structure

Our frontend project follows a modular architecture with clear separation of
concerns:

```
uifort-bo/
├── src/
│   ├── app/                  # Next.js App Router pages and layouts
│   ├── components/           # UI components (organized by purpose)
│   │   ├── application-ui/   # Application-specific components
│   │   ├── base/             # Base/core components
│   │   └── features/         # Feature-specific components
│   ├── constants/            # Global constants
│   ├── contexts/             # React contexts for global state management
│   ├── hocs/                 # Higher-order components
│   ├── hooks/                # Custom React hooks
│   ├── i18n/                 # Internationalization configuration
│   ├── layouts/              # Base layouts
│   ├── mocks/                # Mock data for development and testing
│   ├── router/               # Routing configuration
│   ├── schemas/              # Validation schemas (Zod)
│   ├── services/             # API services
│   ├── stories/              # Storybook documentation
│   ├── theme/                # Theme configuration
│   ├── types/                # Global TypeScript type definitions
│   └── utils/                # Global utility functions
├── public/                   # Static assets
├── .storybook/               # Storybook configuration
└── config/                   # Application configuration files
```

## Component Categories

Components are organized into three main categories based on their purpose and
reusability:

### 1. Base Components (`src/components/base/`)

Base components are the foundation of our UI system. They are highly reusable,
generic components that can be used across the entire application.

**Examples**: Button, Typography, Avatar, Dialog, Label

**Organization**: Grouped by function (data-display, inputs, feedback, etc.)

### 2. Application UI Components (`src/components/application-ui/`)

Application UI components are specific to our application's UI and combine base
components to create more complex UI elements.

**Examples**: HeaderUserDropdown, MerchantSwitcher, FilterSection

**Organization**: Grouped by application area (app-header, app-sidebar,
filter-section, etc.)

### 3. Feature Components (`src/components/features/`)

Feature components are tied to specific business features or domains and often
combine application UI components.

**Examples**: LoginForm, UserDataGrid, AuthGuard

**Organization**: Grouped by business domain (auth, user, etc.)

## Standard Component Structure

All components across all categories follow this consistent structure:

```
components/category/component-name/
├── ComponentName.tsx           # Main component implementation
├── ComponentName.test.tsx      # Unit tests
├── ComponentName.type.ts       # TypeScript type definitions
├── ComponentName.stories.tsx   # Storybook stories
├── ComponentName.constant.ts   # Constants and default values
├── ComponentName.utils.ts      # Component-specific utilities
├── index.ts                    # Public exports
├── parts/                      # Component parts/subcomponents
│   ├── ComponentPart.tsx
│   └── index.ts
├── styles/                     # Styled components
│   ├── ComponentName.style.ts
│   └── ComponentPart.style.ts
└── variants/                   # Component variants
    ├── component-variant-a/
    │   ├── ComponentVariantA.tsx
    │   ├── ComponentVariantA.stories.tsx
    │   ├── ComponentVariantA.test.ts
    │   └── ComponentVariantA.type.ts
    └── component-variant-b/
        ├── ComponentVariantB.tsx
        ├── ComponentVariantB.stories.tsx
        ├── ComponentVariantB.test.ts
        └── ComponentVariantB.type.ts
```

## Services Structure

Services handle API communication and are organized by domain:

```
services/
├── auth/                   # Authentication services
│   ├── auth.service.ts      # Service implementation
│   ├── auth.type.ts         # Service types
│   └── index.ts             # Service exports
└── users/                  # User management services
    ├── user.service.ts      # Service implementation
    ├── user.type.ts         # Service types
    └── index.ts             # Service exports
```

## Hooks Structure

Hooks are organized by their purpose:

```
hooks/
├── auth/                   # Authentication hooks
├── data/                   # Data management hooks
├── lifecycle/              # Component lifecycle hooks
├── navigation/             # Navigation hooks
├── ui/                     # UI-related hooks
└── index.ts                # Hook exports
```

## Contexts Structure

Contexts provide global state management:

```
contexts/
├── auth/                   # Authentication context
├── customization/          # Theme customization context
├── sidebar/                # Sidebar context
└── index.ts                # Context exports
```

## Types Structure

Types are organized in a dedicated directory:

```
types/
├── api-responses.type.ts   # API response type definitions
├── global.d.ts             # Global type declarations
├── mui.d.ts                # Material UI type extensions
└── reset.d.ts              # TypeScript reset types
```

## Best Practices

### Component Organization

1. **Component Categorization**: Categorize components based on their purpose
   and reusability.

   - Base components: Generic, highly reusable
   - Application UI components: Application-specific UI elements
   - Feature components: Business domain-specific components

2. **Component Structure**: Follow the standard component structure for all
   components.

   - Separate types, styles, and component logic
   - Use index.ts files for clean exports
   - Organize variants in subfolders

3. **Component Naming**: Use clear, descriptive names.
   - Base components: Generic names (Button, Typography)
   - Application UI components: Specific names (HeaderUserDropdown)
   - Feature components: Domain-specific names (UserDataGrid)

### Code Organization

1. **Single Responsibility**: Each file should have a single responsibility.
2. **Consistent Imports**: Use consistent import patterns.
3. **Path Aliases**: Use path aliases for cleaner imports.
4. **Type Safety**: Use TypeScript types and interfaces consistently.
5. **API Response Handling**: Follow the standard API response pattern.

### Documentation

1. **JSDoc Comments**: Use JSDoc comments for components, props, and functions.
2. **Storybook Stories**: Create Storybook stories for all components.
3. **MDX Documentation**: Use MDX files for comprehensive documentation.
