# UIFort Back Office

[![Quality Gate Status](https://sonarqube.sgrts.com/api/project_badges/measure?project=aiodintech_uifort-bo_7201b7cf-28d9-49d6-9e2f-b38835d853b3&metric=alert_status&token=sqb_8089ac017a827ee401b9bbe3283f171cf82a3499)](https://sonarqube.sgrts.com/dashboard?id=aiodintech_uifort-bo_7201b7cf-28d9-49d6-9e2f-b38835d853b3)
[![Node Version](https://img.shields.io/badge/node-22.0.0-brightgreen.svg)](https://nodejs.org/)
[![NPM Version](https://img.shields.io/badge/npm-11.0.0-brightgreen.svg)](https://www.npmjs.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-18.0-blue.svg)](https://reactjs.org/)
[![Material-UI](https://img.shields.io/badge/Material--UI-6.0-blue.svg)](https://mui.com/)
[![Next.js](https://img.shields.io/badge/Next.js-15.0-black.svg)](https://nextjs.org/)
[![Vitest](https://img.shields.io/badge/Vitest-3.0.9-purple.svg)](https://vitest.dev/)
[![Storybook](https://img.shields.io/badge/Storybook-8.6.12-purple.svg)](https://storybook.js.org/)

## Overview

A customizable white-label back office solution built with Next.js, providing a flexible and scalable foundation for creating administrative interfaces. This solution emphasizes type safety, modern development practices, and comprehensive testing.

## Key Features

🔐 **Authentication & Authorization**

- Secure sign-in with NextAuth
- Email/Password authentication
- SSO with Google
- Role-based access control

🎨 **UI/UX**

- Material-UI components
- Responsive design
- Dark/Light theme support
- Customizable layouts

🌐 **Internationalization**

- Multi-language support
- RTL support
- Locale-based formatting

🛠 **Developer Experience**

- TypeScript for type safety
- Hot reload in development
- Comprehensive testing setup
- Storybook for component development

## Prerequisites

- Node.js v22.0.0 or later
- npm v11.0.0 or later
- Git

## Quick Start

1. **Clone the repository**

   ```bash
   <NAME_EMAIL>:aiodintech/uifort-bo.git
   cd uifort-bo
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your values
   ```

4. **Start development server**

   ```bash
   npm run dev
   ```

   Access the application at [http://localhost:4000](http://localhost:4000)

## Environment Variables

| Variable                      | Required | Description           | Example                   |
| ----------------------------- | -------- | --------------------- | ------------------------- |
| NEXT_PUBLIC_MUI_X_LICENSE_KEY | Yes      | MUI X license key     | abc123...                 |
| NODE_ENV                      | Yes      | Environment           | development               |
| SONAR_HOST                    | No       | SonarQube URL         | https://sonar.example.com |
| SONAR_PROJECT_KEY             | No       | SonarQube project key | project_key               |
| SONAR_TOKEN                   | No       | SonarQube token       | token123...               |

## Development

### Code Quality

- **Linting**: `npm run lint`
- **Type Checking**: `npm run type-check`
- **Format Code**: `npm run format:write`
- **Run Tests**: `npm run test`
- **Run Unit Tests**: `npm run test:unit`
- **Run Storybook Tests**: `npm run test:storybook`
- **Full Validation**: `npm run validate`

### Testing

- Unit tests with Vitest
- Component testing with React Testing Library
- E2E testing (coming soon)
- Coverage reports: `npm run test:coverage`

### Documentation

- **Storybook**: `npm run storybook`
- **Build Storybook**: `npm run build-storybook`

## CI/CD Pipeline

Our BitBucket pipeline includes:

1. Dependency installation
2. Build verification
3. Test execution
4. Code quality analysis
5. Automated deployment to staging
6. Manual deployment to production

## Contributing

1. Create a feature branch from `develop`
2. Make your changes
3. Run validation: `npm run validate`
4. Submit a pull request
5. Ensure all checks pass

## Troubleshooting

Common issues and solutions:

1. **Build Failures**

   - Clear `.next` directory
   - Remove `node_modules` and reinstall
   - Check Node.js version

2. **Type Errors**
   - Run `npm run type-check` for detailed errors
   - Ensure all dependencies are up to date

## Performance Optimization

- Code splitting enabled
- Image optimization with Next.js
- Bundle size monitoring
- Lazy loading components

## Security

- Dependencies regularly updated
- Security scanning in CI/CD
- Protected environment variables
- Input validation with Zod

## References

- [UIFort Components](https://t3712436.p.clickup-attachments.com/t3712436/daa7f3c7-d51d-4ea5-a6b2-3d587121411f/nextjs-app-typescript-1.0.0.zip)
- [Design Materials](https://app.clickup.com/3712436/v/dc/3h9dm-61096/3h9dm-129776)
- [Next.js Documentation](https://nextjs.org/docs)
- [Material-UI Documentation](https://mui.com/material-ui/getting-started/)
