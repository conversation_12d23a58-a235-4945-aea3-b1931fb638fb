import { describe, expect, it } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';

const {
  ACCESS_LEVELS,
  CACHE_SECOND,
  COMMON_STATUSES,
  MODULE_METHODS,
  MODULE_NAMES,
  <PERSON><PERSON><PERSON><PERSON><PERSON>E_TYPE,
  R<PERSON>ARK_STATUSES,
  REMARK_TYPE,
} = CoreConstant;

describe('Core Constants', () => {
  it('should have correct ACCESS_LEVELS', () => {
    expect(ACCESS_LEVELS).toEqual({
      MERCHANT: 'merchant',
      ORGANIZATION: 'organization',
      ROOT: 'root',
      USER: 'user',
    });
  });

  it('should have correct CACHE_SECOND values', () => {
    expect(CACHE_SECOND).toEqual({
      SHORT: 10,
      MEDIUM: 30,
      STANDARD: 60,
      LONG: 3600,
      DAILY: 86400,
      WEEKLY: 604800,
      NEVER: 0,
    });
  });

  it('should have correct COMMON_STATUSES', () => {
    expect(COMMON_STATUSES).toEqual({
      ACTIVE: 'active',
      DELETED: 'deleted',
      INACTIVE: 'inactive',
    });
  });

  it('should have correct MODULE_METHODS', () => {
    expect(MODULE_METHODS).toEqual({
      CREATE: 'create',
      DELETE: 'delete',
      EXPORT: 'export',
      INDEX: 'index',
      OPTION: 'option',
      UPDATE: 'update',
      UPDATE_BASIC_INFORMATION: 'updateBasicInformation',
      UPDATE_PERSONAL: 'updatePersonal',
      UPDATE_SAFETY: 'updateSafety',
      UPDATE_STATUS: 'updateStatus',
      UPDATE_THEMES: 'updateThemes',
      VIEW: 'view',
    });
  });

  it('should have correct MODULE_NAMES', () => {
    expect(MODULE_NAMES).toEqual({
      ACCESS_CONTROL: 'accessControls',
      AUDIT_TRAIL: 'auditTrails',
      BULK_JOB: 'bulkJobs',
      CORE: 'core',
      DEVELOPER_HUB: 'developerHubs',
      LOCALISATION: 'localisations',
      SETTING: 'settings',
    });
  });

  it('should have correct REMARK_STATUSES', () => {
    expect(REMARK_STATUSES).toEqual({
      ACTIVE: 'active',
      ARCHIVED: 'archived',
    });
  });

  it('should have correct REMARK_TYPE', () => {
    expect(REMARK_TYPE).toEqual({
      AUDIT: 'audit',
      NOTE: 'note',
      SECURITY: 'security',
      SYSTEM: 'system',
      WARNING: 'warning',
    });
  });

  it('should have correct REMARKABLE_TYPE', () => {
    expect(REMARKABLE_TYPE).toEqual({
      IP_ACCESS_CONTROL: 'ip_access_control',
    });
  });
});
