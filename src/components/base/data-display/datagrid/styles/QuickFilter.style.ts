import { styled } from '@mui/material/styles';
import TextField from '@mui/material/TextField';
import { QuickFilter, ToolbarButton } from '@mui/x-data-grid-premium';

type OwnerState = {
  expanded: boolean;
};

/**
 * A styled QuickFilter component.
 */
const StyledQuickFilter = styled(QuickFilter)({
  display: 'grid',
  alignItems: 'center',
});

/**
 * A styled ToolbarButton component for QuickFilter.
 */
const StyledToolbarButton = styled(ToolbarButton)<{ ownerState: OwnerState }>(
  ({ theme, ownerState }) => ({
    gridArea: '1 / 1',
    width: 'min-content',
    height: 'min-content',
    zIndex: 1,
    opacity: ownerState.expanded ? 0 : 1,
    pointerEvents: ownerState.expanded ? 'none' : 'auto',
    transition: theme.transitions.create(['opacity']),
  })
);

/**
 * A styled TextField component for QuickFilter.
 */
const StyledTextField = styled(TextField)<{
  ownerState: OwnerState;
}>(({ theme, ownerState }) => ({
  gridArea: '1 / 1',
  overflowX: 'clip',
  width: ownerState.expanded ? 260 : 'var(--trigger-width)',
  opacity: ownerState.expanded ? 1 : 0,
  transition: theme.transitions.create(['width', 'opacity']),
}));

export { StyledQuickFilter, StyledToolbarButton, StyledTextField };
