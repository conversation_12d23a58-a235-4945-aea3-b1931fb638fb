import { bypassAccessCheck, checkAccess, enforceIPWhitelist } from '#src/utils/access.util.js';
import { describe, expect, it } from 'vitest';
import { swaggerUiConfig } from '#config/swagger.config.js';

describe('Test case for access util', () => {
  let whitelist = ['127.0.0.1'];

  describe('checkAccess', () => {
    it('should allow access for public route when no authAccess', () => {
      const request = {};
      const routeAccess = { public: true };
      const authAccess = undefined;

      expect(checkAccess(request, authAccess, routeAccess)).toBe(true);
    });

    it('should allow access for user role', () => {
      const request = {};
      const routeAccess = { user: true };
      const authAccess = 'user';

      expect(checkAccess(request, authAccess, routeAccess)).toBe(true);
    });

    it('should allow access for member role', () => {
      const request = {};
      const routeAccess = { member: true };
      const authAccess = 'member';

      expect(checkAccess(request, authAccess, routeAccess)).toBe(true);
    });

    it('should allow access for webhook role', () => {
      const request = {};
      const routeAccess = { webhook: true };
      const authAccess = 'webhook';

      expect(checkAccess(request, authAccess, routeAccess)).toBe(true);
    });

    it('should deny access for unmatched roles', () => {
      const request = {};
      const routeAccess = { user: true };
      const authAccess = 'member';

      expect(checkAccess(request, authAccess, routeAccess)).toBe(false);
    });

    it('should deny access when routeAccess is empty', () => {
      const request = {};
      const routeAccess = {};
      const authAccess = 'user';

      expect(checkAccess(request, authAccess, routeAccess)).toBe(false);
    });
  });

  describe('enforceIPWhitelist', () => {
    it('should allow access if IP is whitelisted (x-forwarded-for)', () => {
      const request = {
        headers: { 'x-forwarded-for': '127.0.0.1' },
        ip: '127.0.0.101',
      };

      expect(enforceIPWhitelist(request, whitelist)).toBe(true);
    });

    it('should allow access if IP is whitelisted (fallback to request.ip)', () => {
      const request = {
        headers: {},
        ip: '127.0.0.1',
      };

      expect(enforceIPWhitelist(request, whitelist)).toBe(true);
    });

    it('should deny access if IP is not whitelisted', () => {
      const request = {
        headers: { 'x-forwarded-for': '127.0.0.100' },
        ip: '127.0.0.101',
      };

      expect(enforceIPWhitelist(request, whitelist)).toBe(false);
    });

    it('should deny access when whitelist is empty', () => {
      const request = {
        headers: { 'x-forwarded-for': '127.0.0.1' },
        ip: '127.0.0.101',
      };
      whitelist = [];

      expect(enforceIPWhitelist(request, whitelist)).toBe(false);
    });
  });

  describe('bypassAccessCheck', () => {
    it('should bypass access check for Swagger paths', async () => {
      const request = {
        raw: { url: swaggerUiConfig.routePrefix },
      };
      expect(bypassAccessCheck(request)).toBe(true);
    });

    it('should not bypass access check for api paths', async () => {
      const request = {
        raw: { url: '/api-route' },
      };
      expect(bypassAccessCheck(request)).toBe(false);
    });
  });
});
