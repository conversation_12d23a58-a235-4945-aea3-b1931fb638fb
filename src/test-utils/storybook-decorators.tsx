import { DataGridPremium, Toolbar, type GridPremiumSlotsComponent } from '@mui/x-data-grid-premium';
import type { Decorator } from '@storybook/react';
import { type ReactNode } from 'react';

/**
 * Configuration options for the DataGrid decorator
 */
export interface DataGridDecoratorOptions {
  /** Custom columns for the DataGrid */
  columns?: Array<{ field: string; headerName: string; width?: number }>;
  /** Custom rows data for the DataGrid */
  rows?: Array<Record<string, any>>;
  /** Additional props to pass to DataGridPremium */
  gridProps?: Record<string, any>;
  /** Whether to show the toolbar */
  showToolbar?: boolean;
}

/**
 * Default configuration for DataGrid stories
 */
const DEFAULT_DATAGRID_CONFIG: Required<DataGridDecoratorOptions> = {
  columns: [{ field: 'id', headerName: 'ID' }],
  rows: [],
  gridProps: {},
  showToolbar: true,
};

/**
 * Creates a reusable DataGrid decorator for toolbar components
 * This eliminates the need to repeat the same DataGrid setup across multiple story files
 * 
 * @param options - Configuration options for the DataGrid
 * @returns Storybook decorator function
 * 
 * @example
 * ```typescript
 * // Basic usage
 * const meta = {
 *   component: CustomDensityButton,
 *   decorators: [withDataGridToolbar()],
 * };
 * 
 * // With custom configuration
 * const meta = {
 *   component: CustomExportButton,
 *   decorators: [withDataGridToolbar({
 *     columns: [
 *       { field: 'id', headerName: 'ID' },
 *       { field: 'name', headerName: 'Name' },
 *     ],
 *     rows: [{ id: 1, name: 'Test' }],
 *   })],
 * };
 * ```
 */
export const withDataGridToolbar = (options: DataGridDecoratorOptions = {}): Decorator => {
  const config = { ...DEFAULT_DATAGRID_CONFIG, ...options };
  
  return (Story) => {
    const CustomToolbarComponent = () => (
      <Toolbar>
        <Story />
      </Toolbar>
    );

    const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
      toolbar: CustomToolbarComponent,
    };

    return (
      <DataGridPremium
        columns={config.columns}
        rows={config.rows}
        showToolbar={config.showToolbar}
        slots={defaultSlotProps}
        {...config.gridProps}
      />
    );
  };
};

/**
 * Creates a DataGrid decorator with sample data for more realistic testing
 * 
 * @param additionalOptions - Additional configuration options
 * @returns Storybook decorator function
 */
export const withDataGridToolbarAndData = (additionalOptions: DataGridDecoratorOptions = {}): Decorator => {
  const sampleColumns = [
    { field: 'id', headerName: 'ID', width: 90 },
    { field: 'name', headerName: 'Name', width: 150 },
    { field: 'email', headerName: 'Email', width: 200 },
    { field: 'role', headerName: 'Role', width: 120 },
  ];

  const sampleRows = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'Editor' },
    { id: 4, name: 'Alice Brown', email: '<EMAIL>', role: 'User' },
    { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', role: 'Admin' },
  ];

  return withDataGridToolbar({
    columns: sampleColumns,
    rows: sampleRows,
    ...additionalOptions,
  });
};

/**
 * Creates a simple container decorator for components that don't need DataGrid context
 * 
 * @param containerProps - Props to pass to the container div
 * @returns Storybook decorator function
 */
export const withContainer = (containerProps: React.HTMLAttributes<HTMLDivElement> = {}): Decorator => {
  return (Story) => (
    <div style={{ padding: '20px', ...containerProps.style }} {...containerProps}>
      <Story />
    </div>
  );
};

/**
 * Creates a decorator that wraps the story in a flex container
 * Useful for centering components or creating specific layouts
 * 
 * @param flexProps - CSS flex properties
 * @returns Storybook decorator function
 */
export const withFlexContainer = (flexProps: React.CSSProperties = {}): Decorator => {
  const defaultFlexProps: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '200px',
    padding: '20px',
    ...flexProps,
  };

  return (Story) => (
    <div style={defaultFlexProps}>
      <Story />
    </div>
  );
};

/**
 * Decorator specifically for DataGrid export button with CSV options
 */
export const withDataGridExportToolbar = (csvOptions: Record<string, any> = {}): Decorator => {
  return withDataGridToolbarAndData({
    gridProps: {
      // Add any export-specific grid props here
    },
  });
};
