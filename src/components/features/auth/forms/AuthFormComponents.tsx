import { Button } from '@/components/base/inputs/button';
import { Typography } from '@/components/base/typography';
import { useToggle } from '@/hooks/ui/use-toggle.hook';
import MailOutlineRoundedIcon from '@mui/icons-material/MailOutlineRounded';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import {
  Alert,
  AlertTitle,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  InputAdornment,
  InputLabel,
  OutlinedInput,
  Stack,
} from '@mui/material';
import { type ReactNode } from 'react';

/**
 * Form header component for auth forms
 */
export const FormHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <Container maxWidth="sm">
    <Typography
      align="center"
      variant="h3"
      gutterBottom
    >
      {title}
    </Typography>
    <Typography
      align="center"
      variant="h6"
      fontWeight={400}
    >
      {subtitle}
    </Typography>
  </Container>
);

/**
 * Form container component for auth forms
 */
export const FormContainer = ({ children }: { children: ReactNode }) => (
  <Stack
    mt={{ xs: 2, sm: 3 }}
    justifyContent="center"
    alignItems="center"
    spacing={{ xs: 2, sm: 3 }}
  >
    {children}
  </Stack>
);

/**
 * Email field component for auth forms
 */
export const EmailField = ({ register, errors }: { register: any; errors: any }) => (
  <Grid size={12}>
    <FormControl
      fullWidth
      error={Boolean(errors.email)}
    >
      <InputLabel>Email</InputLabel>
      <OutlinedInput
        {...register('email')}
        label="Email"
        type="email"
        id="email-input"
        placeholder="Write your email"
        startAdornment={
          <InputAdornment position="start">
            <MailOutlineRoundedIcon fontSize="small" />
          </InputAdornment>
        }
      />
      {errors.email && <FormHelperText>{errors.email.message}</FormHelperText>}
    </FormControl>
  </Grid>
);

/**
 * Password field component for auth forms
 */
export const PasswordField = ({ register, errors }: { register: any; errors: any }) => {
  const [showPassword, toggleShowPassword] = useToggle(false);

  return (
    <Grid size={12}>
      <FormControl
        fullWidth
        error={Boolean(errors.password)}
      >
        <InputLabel>Password</InputLabel>
        <OutlinedInput
          {...register('password')}
          label="Password"
          type={showPassword ? 'text' : 'password'}
          id="password-input"
          placeholder="Write your password"
          endAdornment={
            <InputAdornment position="end">
              <Button
                variant="outlined"
                color="secondary"
                sx={{ mr: -0.8 }}
                onClick={toggleShowPassword}
                customStyle="icon"
              >
                {showPassword ? (
                  <VisibilityOff fontSize="small" />
                ) : (
                  <Visibility fontSize="small" />
                )}
              </Button>
            </InputAdornment>
          }
        />
        {errors.password && <FormHelperText>{errors.password.message}</FormHelperText>}
      </FormControl>
    </Grid>
  );
};

/**
 * Submit button component for auth forms
 */
export const SubmitButton = ({ label, isPending }: { label: string; isPending: boolean }) => (
  <Grid size={12}>
    <Button
      disabled={isPending}
      variant="contained"
      type="submit"
      size="large"
      fullWidth
    >
      {label}
    </Button>
  </Grid>
);

/**
 * Form error component for auth forms
 */
export const FormError = ({ error }: { error?: string }) => {
  if (!error) return null;

  return (
    <Grid size={12}>
      <Alert severity="error">
        {/* <AlertTitle>Error</AlertTitle> */}
        {error}
      </Alert>
    </Grid>
  );
};

/**
 * Form success component for auth forms
 */
export const FormSuccess = ({ message }: { message?: string }) => {
  if (!message) return null;

  return (
    <Grid size={12}>
      <Alert severity="success">
        {/* <AlertTitle>Success</AlertTitle> */}
        {message}
      </Alert>
    </Grid>
  );
};

/**
 * Demo credentials component for login form
 */
export const DemoCredentials = () => (
  <Grid size={12}>
    <Alert severity="warning">
      <AlertTitle>Sign in credentials</AlertTitle>
      Email <b><EMAIL></b> and password <b>DemoPass123</b>
    </Alert>
  </Grid>
);
