import { RouterLink } from '@/components/base/navigation/router-link';
import { Typography } from '@/components/base/typography';
import { usePathname } from '@/hooks/navigation/use-pathname.hook';
import { isRouteActive } from '@/utils/route.util';
import KeyboardArrowRightTwoToneIcon from '@mui/icons-material/KeyboardArrowRightTwoTone';
import { Box, Collapse, List, ListItemText, useMediaQuery, type Theme } from '@mui/material';
import { useState } from 'react';
import type { NavItemProps, SidebarNavMenuProps } from '../Sidebar.type';
import {
  ListItemButtonWrapper,
  ListSubheaderWrapper,
  SubMenu,
} from '../styles/SidebarNavMenu.style';

/**
 * Navigation item component
 */
const NavItem = ({ item }: NavItemProps) => {
  const { title, path, items } = item;
  const pathname = usePathname();
  const isActive = path ? isRouteActive(path, pathname) : false;

  // Check if any sub-menu item is active
  const isSubMenuActive = items?.some((sub) => {
    return (
      (sub.path && isRouteActive(sub.path, pathname)) ||
      sub.items?.some((nestedSub) => nestedSub.path && isRouteActive(nestedSub.path, pathname))
    );
  });

  const [open, setOpen] = useState(isSubMenuActive);

  const handleToggle = () => {
    if (items) {
      setOpen(!open);
    }
  };

  return (
    <Box px={2}>
      <ListItemButtonWrapper
        selected={Boolean(isActive || isSubMenuActive)}
        onClick={handleToggle}
        //  @ts-ignore
        component={path ? RouterLink : 'a'}
        href={path ?? undefined}
      >
        <ListItemText disableTypography>
          <Typography caseTransform={item.caseTransform ?? 'sentenceCase'}>{title}</Typography>
        </ListItemText>
        {items && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
              transition: (theme) => theme.transitions.create(['transform']),
            }}
          >
            <KeyboardArrowRightTwoToneIcon fontSize="small" />
          </Box>
        )}
      </ListItemButtonWrapper>
      {items && (
        <Collapse in={open}>
          <SubMenu
            component="div"
            sx={{ mx: -2 }}
          >
            {items.map((subItem) => (
              <NavItem
                key={subItem.title}
                item={subItem}
              />
            ))}
          </SubMenu>
        </Collapse>
      )}
    </Box>
  );
};

/**
 * Sidebar navigation menu component
 */
export const SidebarNavMenu = ({ menuItems = [] }: SidebarNavMenuProps) => {
  const mdUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('md'));

  return (
    <Box>
      {menuItems.map((menuItem) => (
        <div key={menuItem.title}>
          {menuItem.isHeader ? (
            <List
              component="nav"
              subheader={
                <ListSubheaderWrapper
                  component="div"
                  disableSticky={!mdUp}
                >
                  {menuItem.title}
                </ListSubheaderWrapper>
              }
            >
              {menuItem.items?.map((subItem) => (
                <NavItem
                  key={subItem.title}
                  item={subItem}
                />
              ))}
            </List>
          ) : (
            <NavItem item={menuItem} />
          )}
        </div>
      ))}
    </Box>
  );
};

export default SidebarNavMenu;
