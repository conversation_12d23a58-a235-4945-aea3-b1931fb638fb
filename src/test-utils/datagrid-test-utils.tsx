import { DataGridPremium, Toolbar, type GridPremiumSlotsComponent } from '@mui/x-data-grid-premium';
import { type FC, type ReactNode } from 'react';
import { vi } from 'vitest';

/**
 * Creates a DataGrid wrapper component for testing DataGrid toolbar components
 * @param children - The toolbar component(s) to render
 * @param gridProps - Additional props to pass to DataGridPremium
 * @returns React functional component that wraps children in a DataGrid
 */
export const createDataGridWrapper = (
  children: ReactNode,
  gridProps: Record<string, any> = {}
): FC => {
  return () => {
    const CustomToolbarComponent = () => <Toolbar>{children}</Toolbar>;

    const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
      toolbar: CustomToolbarComponent,
    };

    return (
      <DataGridPremium
        columns={[{ field: 'id', headerName: 'ID' }]}
        rows={[]}
        showToolbar
        slots={defaultSlotProps}
        {...gridProps}
      />
    );
  };
};

/**
 * Creates mock functions for DataGrid API operations
 * @param customMocks - Additional mock functions to include
 * @returns Object containing mock functions for common DataGrid operations
 */
export const createDataGridApiMocks = (customMocks: Record<string, any> = {}) => {
  const defaultMocks = {
    setDensity: vi.fn(),
    exportDataAsCsv: vi.fn(),
    setFilterModel: vi.fn(),
    setQuickFilterValues: vi.fn(),
    showColumnMenu: vi.fn(),
    hideColumnMenu: vi.fn(),
    setColumnVisibilityModel: vi.fn(),
    ...customMocks,
  };

  return {
    current: defaultMocks,
  };
};

/**
 * Creates a mock for @mui/x-data-grid-premium with customizable behavior
 * @param customMocks - Custom mock implementations
 * @returns Vitest mock function
 */
export const createDataGridMock = (customMocks: Record<string, any> = {}) => {
  const defaultMocks = {
    gridDensitySelector: 'gridDensitySelector',
    useGridSelector: () => 'standard',
    useGridApiContext: () => createDataGridApiMocks(),
    gridPaginatedVisibleSortedGridRowIdsSelector: vi.fn(),
    gridSortedRowIdsSelector: vi.fn(),
    gridExpandedSortedRowIdsSelector: vi.fn(),
    ...customMocks,
  };

  return vi.mock('@mui/x-data-grid-premium', async () => {
    const actual = await vi.importActual('@mui/x-data-grid-premium');
    return {
      ...actual,
      ...defaultMocks,
    };
  });
};

/**
 * Default test data for DataGrid components
 */
export const TEST_GRID_DATA = {
  columns: [
    { field: 'id', headerName: 'ID', width: 90 },
    { field: 'name', headerName: 'Name', width: 150 },
    { field: 'email', headerName: 'Email', width: 200 },
  ],
  rows: [
    { id: 1, name: 'John Doe', email: '<EMAIL>' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>' },
  ],
};

/**
 * Common test scenarios for DataGrid density options
 */
export const DENSITY_TEST_SCENARIOS = [
  { value: 'compact', label: 'compact' },
  { value: 'standard', label: 'standard' },
  { value: 'comfortable', label: 'comfortable' },
] as const;
