import { beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import * as cacheUtil from '#src/utils/cache.util.js';
import geoip from 'geoip-lite';
import onReadyHook from '#src/hooks/onReady.hook.js';
import { spawn } from 'child_process';

vi.mock('geoip-lite');
vi.mock('child_process', () => ({
  spawn: vi.fn().mockImplementation(() => {
    return {
      stdout: {
        on: vi.fn(),
      },
      stderr: {
        on: vi.fn(),
      },
      on: vi.fn(),
    };
  }),
}));

describe('Test case for onReady hook', () => {
  let fastify;
  let onReadyFunction;

  beforeEach(async () => {
    vi.resetAllMocks();
    vi.useFakeTimers();

    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
    };

    // Mock the kafka
    fastify.decorate('config', {
      KAFKA: true,
      KAFKA_BROKERS: 'localhost:9092',
      GEOIP_LICENSE_KEY: 'dummy-key',
    });

    fastify.kafka = {
      producer: {
        connect: vi.fn().mockResolvedValue(),
        send: vi.fn().mockResolvedValue(),
      },
      admin: {
        listTopics: vi.fn().mockResolvedValue(['existing-topic']),
        createTopics: vi.fn().mockResolvedValue(),
      },
    };

    // Mock the redis
    fastify.redis = {};

    // Define and configure the mock ChildProcess instance for `spawn`
    const mockStdout = { on: vi.fn() };
    const mockStderr = { on: vi.fn() };
    const mockChildProcessInstance = {
      stdout: mockStdout,
      stderr: mockStderr,
      on: vi.fn((event, cb) => {
        // This mock ensures that when `onReadyHook` calls `updatedb.on('close', ...)`,
        // the callback `cb(0)` is immediately executed, triggering `geoip.reloadData`.
        if (event === 'close') {
          cb(0); // Simulate successful close
        }
      }),
    };

    // Make `spawn` return our configured mock instance for every test
    spawn.mockReturnValue(mockChildProcessInstance);

    // Also, configure `geoip.reloadData` for every test
    geoip.reloadData.mockImplementation((cb) => cb());

    // Register the hook
    await fastify.register(onReadyHook);

    // Extract the function
    onReadyFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onReady')[1];

    vi.spyOn(cacheUtil, 'getCacheKeysWithPrefix');
    vi.spyOn(cacheUtil, 'getCache');
    vi.spyOn(cacheUtil, 'clearCache');
  });

  it('should register the onReady hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onReady', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onReady hook
    await onReadyFunction();

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onReady hook');
  });

  it('should create audit-trails topic if not already present', async () => {
    fastify.kafka.admin.listTopics.mockResolvedValueOnce([]);
    fastify.kafka.admin.createTopics.mockResolvedValueOnce();

    await onReadyFunction();

    // Check if the admin client tried to create the audit-trails topic
    expect(fastify.kafka.admin.createTopics).toHaveBeenCalledWith({
      topics: [
        {
          topic: 'audit-trails',
          numPartitions: 50,
          replicationFactor: 1,
        },
      ],
    });
  });

  it('should not create the audit-trails topic if it already exists', async () => {
    fastify.kafka.admin.listTopics.mockResolvedValueOnce(['audit-trails']);

    await onReadyFunction();

    // Ensure the createTopics method was never called, as the topic already exists
    expect(fastify.kafka.admin.createTopics).not.toHaveBeenCalled();
  });

  it('should skip retry if no failed Kafka messages found', async () => {
    cacheUtil.getCacheKeysWithPrefix.mockResolvedValue([]);
    await onReadyFunction();
    vi.advanceTimersByTime(10000);
    expect(cacheUtil.getCacheKeysWithPrefix).toHaveBeenCalled();
  });

  it('should update and reload GeoIP data', async () => {
    const mockStdout = { on: vi.fn() };
    const mockStderr = { on: vi.fn() };
    const mockChild = {
      stdout: mockStdout,
      stderr: mockStderr,
      on: vi.fn((event, cb) => {
        if (event === 'close') {
          cb(0);
        }
      }),
    };

    spawn.mockReturnValue(mockChild);
    geoip.reloadData = vi.fn((cb) => cb());

    await onReadyFunction();

    expect(spawn).toHaveBeenCalledWith(expect.stringContaining('node'), [
      './node_modules/geoip-lite/scripts/updatedb.js',
      'license_key=dummy-key',
    ]);
    expect(geoip.reloadData).toHaveBeenCalled();
    expect(fastify.log.info).toHaveBeenCalledWith('GeoIP data reloaded into memory.');
  });

  it('should log error if GeoIP update fails', async () => {
    const mockChild = {
      stdout: { on: vi.fn() },
      stderr: { on: vi.fn((_, cb) => cb('GeoIP error')) },
      on: vi.fn(),
    };
    spawn.mockReturnValue(mockChild);

    await onReadyFunction();

    expect(fastify.log.error).toHaveBeenCalledWith('GeoIP update failed: GeoIP error');
  });

  it('should log error if GeoIP license key missing', async () => {
    fastify.config.GEOIP_LICENSE_KEY = undefined;

    await onReadyFunction();

    expect(fastify.log.warn).toHaveBeenCalledWith('GeoIP license key missing. Skipping update.');
    expect(spawn).not.toHaveBeenCalled();
    expect(geoip.reloadData).not.toHaveBeenCalled();
  });
});
