import { EventActionsConstant } from '../modules/audit-trail/constants/index.js';
import { deepRedact } from '#src/utils/response-modifier.util.js';
import geoip from 'geoip-lite';
import { setCache } from '#src/utils/cache.util.js';

/**
 * This function generates event details based on the provided parameters.
 *
 * @param {Object} request - The request object containing user and entity information.
 * @param {string} referenceIds - The reference IDs associated with the event.
 *
 * @returns {Object} An object containing the module name, event name, and event description.
 */
const generateEvents = async (request, referenceIds, status) => {
  const username = request?.authInfo?.username || 'UNKNOWN_USER';
  const hierarchy = request?.entity?.hierarchy || 'UNKNOWN_HIERARCHY';
  const entityName = request?.entity?.name || 'UNKNOWN_ENTITY';

  const formattedEvent = formatTitle(request.auditEntries.event);

  const actionDetails =
    EventActionsConstant.EVENT_ACTION_DESCRIPTIONS[request.auditEntries.action] ||
    EventActionsConstant.EVENT_ACTION_DESCRIPTIONS['DEFAULT'];

  const name = actionDetails.name(formattedEvent);

  const description = actionDetails.description(
    username,
    formattedEvent,
    referenceIds,
    hierarchy,
    entityName,
    status,
  );

  const moduleName = formatTitle(request.auditEntries.module);

  return { moduleName, name, description };
};

/**
 * This function formats a given string by capitalizing the first letter of each word and replacing underscores with spaces.
 *
 * @param {string} str - The input string to be formatted.
 *
 * @returns {string} The formatted string. If the input is not a string, an empty string is returned.
 */
const formatTitle = (str) => {
  if (typeof str !== 'string') {
    return '';
  }

  const formattedStr =
    str
      .toLowerCase()
      .split('_')
      .map((w) => w.charAt(0).toUpperCase() + w.slice(1))
      .join(' ') || '';
  return formattedStr;
};

/**
 * Builds and populates audit trail entries for each request.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {FastifyRequest} request - The Fastify request object
 *
 * @returns {void}
 */
const buildAuditTrailEntry = (fastify, request) => {
  request.auditEntries = [];

  // Build audit trail entry
  const organization = formatEntityInfo(request.parentEntity);
  const merchant = formatEntityInfo(request.entity);

  request.auditEntries = {
    timestamp: new Date().toISOString(),
    entityAccessId: request.entity?.id || 'UNKNOWN_ACCESS_ID',
    hierarchyLevel: request.entity?.hierarchy || 'UNKNOWN_HIERARCHY_LEVEL',
    module: null,
    event: null,
    action: null,
    actor: {
      userType: request.authInfo?.authAccess || 'UNKNOWN_ACTOR_TYPE',
      userId: request.authInfo?.id || 'UNKNOWN_ACTOR_ID',
      username: request.authInfo?.username || 'UNKNOWN_ACTOR_USERNAME',
      role: request.authInfo?.role || 'UNKNOWN_ACTOR_ROLE',
      department: request.authInfo?.department || 'UNKNOWN_ACTOR_DEPARTMENT',
      organization,
      merchant,
    },
    target: [],
    details: {
      request: {
        requestId: request.id || 'UNKNOWN_REQUEST_ID',
        statusCode: null,
        path: request.url || 'UNKNOWN_REQUEST_URL',
        method: request.method || 'UNKNOWN_REQUEST_METHOD',
        parameters: request.query || 'UNKNOWN_REQUEST_QUERY_PARAMETERS',
        payload: request.body || {},
      },
      error: {},
      metrics: {},
    },
    context: {
      ip: request.headers?.['x-forwarded-for'] || 'UNKNOWN_IP',
      location: getLocation(request),
      userAgent: request.headers?.['x-user-agent'] || 'UNKNOWN_USER_AGENT',
      device: getDeviceInfo(request),
      fingerprintId: request.fingerprintId || 'UNKNOWN_FINGERPRINT_ID',
      host: request.headers?.['origin'] || 'UNKNOWN_HOST',
    },
    status: null,
    description: null,
  };
};

const formatEntityInfo = (entity, fallback = 'UNKNOWN') => {
  return {
    id: entity?.id || `${fallback}_ID`,
    code: entity?.project_code || `${fallback}_PROJECT_CODE`,
    prefix: entity?.prefix || `${fallback}_PREFIX`,
    name: entity?.name || `${fallback}_NAME`,
  };
};

const getLocation = (request) => {
  const ip = request.ip || request.headers?.['x-forwarded-for'];
  if (!ip) {
    return 'UNKNOWN_LOCATION';
  }

  const geo = geoip.lookup(ip);
  if (!geo) {
    return 'UNKNOWN_LOCATION';
  }

  return `${geo.city || 'UNKNOWN_CITY'}, ${geo.country || 'UNKNOWN_COUNTRY'}`;
};

const getDeviceInfo = (request) => {
  const ua = request.headers?.['x-user-agent'] || '';
  if (/mobile/i.test(ua)) {
    return 'Mobile';
  }
  if (/tablet/i.test(ua)) {
    return 'Tablet';
  }
  if (/Macintosh|Mac OS X/i.test(ua)) {
    return 'Mac';
  }
  if (/Windows/i.test(ua)) {
    return 'Windows PC';
  }
  if (/Linux/i.test(ua)) {
    return 'Linux PC';
  }
  return 'UNKNOWN_DEVICE';
};

/**
 * Function to handle audit trail logging for API requests.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {IncomingMessage} request - The incoming HTTP request
 * @param {ServerResponse} reply - The HTTP response
 *
 * @returns {void}
 */
const finalizeAuditTrailEntry = async (fastify, request, reply) => {
  request.auditEntries = request.auditEntries ?? {};
  request.auditEntries.details = request.auditEntries.details ?? {};
  request.auditEntries.details.request = request.auditEntries.details.request ?? {};
  request.auditEntries.details.error = request.auditEntries.details.error ?? {};

  // Populate request details
  request.auditEntries.status = 'Success';
  request.auditEntries.details.request.statusCode = reply.statusCode;

  if (reply.statusCode >= 400) {
    request.auditEntries.status = 'Failed';
    request.auditEntries.details.error.message = request.responsePayload?.message;
    request.auditEntries.details.error.errorCode = request.responsePayload?.errorCode;
  }

  // Redact sensitive fields
  const redactedAuditEntry = deepRedact(request.auditEntries);

  const message = [
    {
      key: request.authInfo?.id,
      value: JSON.stringify(redactedAuditEntry),
    },
  ];

  // Send audit trail to Kafka
  if (fastify.config.KAFKA) {
    try {
      await fastify.kafka.producer.send({
        topic: 'audit-trails',
        messages: message,
      });
    } catch (error) {
      // If an error occurs while sending the audit trail to Kafka, store the audit trail in Redis
      fastify.log.debug(error, 'Kafka send failed');
      const cacheKey = `failed-kafka-message:${request.entity?.id}:${request.authInfo?.id}:${Date.now()}`;
      setCache(fastify.redis, cacheKey, message);
      fastify.log.debug(message, 'Kafka message stored in Redis');
    }
  }
};

/**
 * Sets audit trail metadata for the provided request.
 *
 * @param {Object} request - The request object containing audit trail information.
 * @param {Object} meta - An object containing the module, event, and action for the audit trail.
 * @param {string} [id=''] - An optional reference ID for the audit trail.
 *
 * @returns {Promise<void>} A promise that resolves when the audit trail metadata is set.
 */
const setAuditMeta = async (request, { module, event, action }, id = '') => {
  if (!request.auditEntries) {
    request.auditEntries = {};
  }

  // Set audit trail metadata
  request.auditEntries.module = module;
  request.auditEntries.event = event;
  request.auditEntries.action = action;

  // Generate event details
  const eventInfo = await generateEvents(request, id);

  // Set audit trail description
  request.auditEntries.description = eventInfo?.description;
};

export { generateEvents, buildAuditTrailEntry, finalizeAuditTrailEntry, setAuditMeta };
