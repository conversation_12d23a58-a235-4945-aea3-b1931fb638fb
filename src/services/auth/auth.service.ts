import ROUTES from '@/router/routes';
import { type ApiResponse } from '@/types/api-responses.type';
import type { User } from '../users/user.type';
import { type AuthService, type LoginParams, type ResetPasswordParams } from './auth.type';

/**
 * Generate a random token for demo purposes
 */
const generateToken = (): string => {
  const arr = new Uint8Array(12);
  window.crypto.getRandomValues(arr);
  return Array.from(arr, (v) => v.toString(16).padStart(2, '0')).join('');
};

/**
 * Mock user data for demo purposes
 */
const mockUser: User = {
  id: '489567',
  avatar: '/avatars/2.png',
  firstName: 'Clara',
  lastName: '<PERSON>',
  username: 'claramartinez',
  email: '<EMAIL>',
  accessId: '123456789012',
  role: 'Administrator',
  jobtitle: 'Principal Engineer',
};

/**
 * Authentication service implementation
 *
 * This is currently a mock implementation for demo purposes.
 * In a real application, this would make actual API calls.
 */
class AuthServiceImpl implements AuthService {
  /**
   * Sign in with email and password
   *
   * @param params - Login parameters
   * @returns API response
   */
  signInWithPassword = async (params: LoginParams): Promise<ApiResponse<void>> => {
    // In a real application, this would be an API call
    // return apiRequest('/auth/login', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(params),
    // });

    // Mock implementation for demo
    const { email, password } = params;

    if (email !== '<EMAIL>' || password !== 'DemoPass123') {
      return {
        message: 'Please ensure your credentials are correct',
        errorCode: 'AUTH_INVALID_CREDENTIALS',
        meta: {},
      };
    }

    const token = generateToken();
    localStorage.setItem('uifort-authentication', token);

    return {
      message: 'Login successful',
      data: undefined,
      meta: {},
    };
  };

  /**
   * Reset password
   *
   * @param params - Reset password parameters
   * @returns API response
   */
  resetPassword = async (_: ResetPasswordParams): Promise<ApiResponse<void>> => {
    // In a real application, this would be an API call
    // return apiRequest('/auth/reset-password', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(params),
    // });

    // Mock implementation for demo
    return {
      message: 'This functionality is not available in demo mode',
      errorCode: 'DEMO_MODE_RESTRICTION',
      meta: {},
    };
  };

  /**
   * Get current user
   *
   * @returns API response with user data
   */
  getUser = async (): Promise<ApiResponse<User | null>> => {
    // In a real application, this would be an API call
    // return apiRequest('/auth/me', {
    //   headers: withAuth(),
    // });

    // Mock implementation for demo
    const token = localStorage.getItem('uifort-authentication');

    if (!token) {
      return {
        message: 'No active session found',
        data: null,
        meta: {},
      };
    }

    return {
      message: 'User session retrieved successfully',
      data: mockUser,
      meta: {},
    };
  };

  /**
   * Sign out
   *
   * @returns API response
   */
  signOut = async (): Promise<ApiResponse<void>> => {
    // In a real application, this would be an API call
    // return apiRequest('/auth/logout', {
    //   method: 'POST',
    //   headers: withAuth(),
    // });

    // Mock implementation for demo
    localStorage.removeItem('uifort-authentication');

    window.location.replace(ROUTES.AUTH.LOGIN);

    return {
      message: 'Signed out successfully',
      data: undefined,
      meta: {},
    };
  };
}

export const authService = new AuthServiceImpl();
