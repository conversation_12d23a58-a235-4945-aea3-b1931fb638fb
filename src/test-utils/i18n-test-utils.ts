import { vi } from 'vitest';

/**
 * Common translation keys used across DataGrid components
 */
export const COMMON_TRANSLATIONS = {
  // Density button translations
  'common.button.density': 'density',
  'common.label.comfortable': 'comfortable',
  'common.label.compact': 'compact',
  'common.label.selectDensity': 'select density',
  'common.label.standard': 'standard',
  
  // Export button translations
  'common.button.export': 'export',
  'common.label.selectExport': 'select export',
  'common.label.exportAll': 'export all',
  'common.label.exportCurrentPage': 'export current page',
  'common.label.exportFiltered': 'export filtered',
  
  // Filter button translations
  'common.button.filters': 'filters',
  'common.label.selectFilters': 'select filters',
  
  // Common labels
  'common.button.cancel': 'cancel',
  'common.button.save': 'save',
  'common.button.delete': 'delete',
  'common.button.edit': 'edit',
  'common.label.loading': 'loading',
  'common.label.error': 'error',
} as const;

/**
 * Creates a mock for react-i18next with custom translations
 * @param translations - Custom translation object to merge with common translations
 * @returns Vitest mock function
 */
export const createI18nMock = (translations: Record<string, string> = {}) => {
  const allTranslations = { ...COMMON_TRANSLATIONS, ...translations };
  
  return vi.mock('react-i18next', () => ({
    useTranslation: () => ({
      t: (key: string) => allTranslations[key] ?? key,
      i18n: {
        language: 'en',
        changeLanguage: vi.fn(),
      },
    }),
    Trans: ({ children }: { children: React.ReactNode }) => children,
    initReactI18next: {
      type: '3rdParty',
      init: vi.fn(),
    },
  }));
};

/**
 * Creates a mock translation function for use in utility tests
 * @param translations - Custom translation object
 * @returns Mock translation function
 */
export const createMockT = (translations: Record<string, string> = {}) => {
  const allTranslations = { ...COMMON_TRANSLATIONS, ...translations };
  return vi.fn((key: string) => allTranslations[key] ?? key);
};
