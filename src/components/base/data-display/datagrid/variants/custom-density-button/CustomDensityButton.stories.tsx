import { withDataGridToolbar } from '@/test-utils/storybook-decorators';
import { getByXPath } from '@/utils/xpath-selector.util';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, screen, waitFor, within } from 'storybook/test';
import CustomDensityButton from './CustomDensityButton';

/**
 * Storybook metadata configuration for the CustomDensityButton component.
 */
const meta = {
  component: CustomDensityButton,
  title: 'Components/base/data-display/datagrid/CustomDensityButton',
  tags: ['autodocs'],
  decorators: [withDataGridToolbar()],
} satisfies Meta<typeof CustomDensityButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomDensityButton component.
 */
export const Default: Story = {
  play: async ({ canvas, userEvent, step }) => {
    await step('Able to show tooltip', async () => {
      await userEvent.hover(canvas.getByText('common.button.density'));

      const tooltip = await waitFor(() =>
        getByXPath(
          `//div[@role="tooltip" and contains(@class, "MuiTooltip-popper") and contains(., "common.label.selectDensity")]`,
          document
        )
      );

      expect(tooltip).toBeInTheDocument();
    });

    await step('Click density button', async () => {
      await userEvent.click(canvas.getByText('common.button.density'));
    });

    const menu = await waitFor(() => screen.getByRole('menu'));
    await step('Menu is shown when clicked', async () => {
      expect(menu).toBeInTheDocument();
    });

    await step('Menu item is correct', async () => {
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(3);

      expect(within(menu).getByText(/compact/i)).toBeInTheDocument();
      expect(within(menu).getByText(/standard/i)).toBeInTheDocument();
      expect(within(menu).getByText(/comfortable/i)).toBeInTheDocument();

      expect(within(menu).getByTestId('ViewHeadlineIcon')).toBeInTheDocument();
      expect(within(menu).getByTestId('TableRowsIcon')).toBeInTheDocument();
      expect(within(menu).getByTestId('ViewStreamIcon')).toBeInTheDocument();
    });

    await step('Menu item default should select standard', async () => {
      const selected = getByXPath(
        '//li[contains(@class, "Mui-selected") and contains(.,"standard")]',
        document
      );

      expect(selected).toBeInTheDocument();
    });

    await step('Menu can be closed with escape button', async () => {
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
      });
    });
  },
};

/**
 * Story showing the density button with sample data for better visual context
 */
export const WithSampleData: Story = {
  decorators: [
    withDataGridToolbar({
      columns: [
        { field: 'id', headerName: 'ID', width: 90 },
        { field: 'name', headerName: 'Name', width: 150 },
        { field: 'email', headerName: 'Email', width: 200 },
      ],
      rows: [
        { id: 1, name: 'John Doe', email: '<EMAIL>' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>' },
        { id: 3, name: 'Bob Johnson', email: '<EMAIL>' },
      ],
    }),
  ],
  parameters: {
    docs: {
      description: {
        story: 'Density button with sample data to demonstrate visual impact of density changes.',
      },
    },
  },
};
