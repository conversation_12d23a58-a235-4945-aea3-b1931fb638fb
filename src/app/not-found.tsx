'use client';

import { WarningAvatar } from '@/components/base/data-display/avatar';
import { Button } from '@/components/base/inputs/button';
import { RouterLink } from '@/components/base/navigation/router-link';
import { Typography } from '@/components/base/typography';
import ROUTES from '@/router/routes';
import WestRoundedIcon from '@mui/icons-material/WestRounded';
import { Box, Container, Divider, Stack } from '@mui/material';
import { useTranslation } from 'react-i18next';

const NotFoundPage = () => {
  const { t } = useTranslation();

  return (
    <Container maxWidth="sm">
      <Stack
        spacing={4}
        justifyContent="center"
        alignItems="center"
        direction="column"
        textAlign="center"
        py={8}
      >
        <WarningAvatar size={84} />

        <Typography
          variant="h2"
          fontWeight={700}
          color="text.primary"
          gutterBottom
          caseTransform="sentenceCase"
        >
          {t('page not found')}
        </Typography>

        <Typography
          variant="h4"
          fontWeight={500}
          color="text.secondary"
          caseTransform="sentenceCase"
        >
          {t('we moved the content to a different page')}
        </Typography>

        <Divider sx={{ width: '100%' }}>
          <Box
            sx={{
              width: 60,
              height: 4,
              backgroundColor: 'primary.main',
              borderRadius: 2,
            }}
          />
        </Divider>

        <Button
          variant="outlined"
          color="secondary"
          component={RouterLink}
          href={ROUTES.INDEX}
          startIcon={<WestRoundedIcon />}
        >
          {t('go to homepage')}
        </Button>
      </Stack>
    </Container>
  );
};

export default NotFoundPage;
