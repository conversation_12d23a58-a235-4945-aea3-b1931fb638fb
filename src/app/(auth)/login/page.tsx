'use client';

import { AuthLayout } from '@/components/application-ui/layouts/auth-layout';
import { LoginForm } from '@/components/features/auth/forms';
import { GuestGuard as Layout } from '@/components/features/auth/guards/guest-guard';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type JSX } from 'react';

const PageContent = (): JSX.Element => {
  return (
    <AuthLayout>
      <LoginForm />
    </AuthLayout>
  );
};

const Page = (): JSX.Element => {
  usePageTitle('sign in');
  return (
    <Layout>
      <PageContent />
    </Layout>
  );
};

export default Page;
