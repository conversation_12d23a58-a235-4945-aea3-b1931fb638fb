import { beforeEach, describe, expect, it, vi } from 'vitest';
import { applyAuditFields } from '#src/mixins/auditable.mixin.js';

describe('Auditable Mixin', () => {
  let mockModel;
  let mockInstance;

  beforeEach(() => {
    mockInstance = {
      set: vi.fn(),
      changed: vi.fn(),
    };

    mockModel = {
      beforeCreate: vi.fn(),
      beforeUpdate: vi.fn(),
    };

    applyAuditFields(mockModel);
  });

  describe('beforeCreate hook', () => {
    it('should set createdBy and updatedBy when userId is provided', () => {
      const beforeCreateHook = mockModel.beforeCreate.mock.calls[0][0];
      beforeCreateHook(mockInstance, { authInfoId: 'user123' });

      expect(mockInstance.set).toHaveBeenCalledTimes(2);
      expect(mockInstance.set).toHaveBeenCalledWith('createdBy', 'user123');
      expect(mockInstance.set).toHaveBeenCalledWith('updatedBy', 'user123');
    });

    it('should not set createdBy and updatedBy when authInfoId is not provided', () => {
      const beforeCreateHook = mockModel.beforeCreate.mock.calls[0][0];
      beforeCreateHook(mockInstance, {});

      expect(mockInstance.set).not.toHaveBeenCalled();
    });
  });

  describe('beforeUpdate hook', () => {
    it('should set updatedBy when userId is provided and fields have changed', () => {
      mockInstance.changed.mockReturnValue(['someField']);
      const beforeUpdateHook = mockModel.beforeUpdate.mock.calls[0][0];
      beforeUpdateHook(mockInstance, { authInfoId: 'user456' });

      expect(mockInstance.changed).toHaveBeenCalledTimes(1);
      expect(mockInstance.set).toHaveBeenCalledTimes(1);
      expect(mockInstance.set).toHaveBeenCalledWith('updatedBy', 'user456');
    });

    it('should not set updatedBy when userId is provided but no fields have changed', () => {
      mockInstance.changed.mockReturnValue([]);
      const beforeUpdateHook = mockModel.beforeUpdate.mock.calls[0][0];
      beforeUpdateHook(mockInstance, { authInfoId: 'user456' });

      expect(mockInstance.changed).toHaveBeenCalledTimes(1);
      expect(mockInstance.set).not.toHaveBeenCalled();
    });

    it('should not set updatedBy when authInfoId is not provided', () => {
      const beforeUpdateHook = mockModel.beforeUpdate.mock.calls[0][0];
      beforeUpdateHook(mockInstance, {});

      expect(mockInstance.changed).not.toHaveBeenCalled();
      expect(mockInstance.set).not.toHaveBeenCalled();
    });
  });

  it('should apply both beforeCreate and beforeUpdate hooks', () => {
    expect(mockModel.beforeCreate).toHaveBeenCalledTimes(1);
    expect(mockModel.beforeUpdate).toHaveBeenCalledTimes(1);
  });
});
