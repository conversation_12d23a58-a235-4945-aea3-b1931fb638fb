import { Box, Grid, Paper } from '@mui/material';
import { type JSX } from 'react';
import { type AuthLayoutProps } from './AuthLayout.type';

/**
 * AuthLayout is a component that provides a consistent layout for authentication pages.
 *
 * It centers the content in a paper container with appropriate spacing and styling.
 * This component should be used for login, signup, password reset, and other auth-related pages.
 *
 * @example
 * ```tsx
 * <AuthLayout>
 *   <LoginForm />
 * </AuthLayout>
 * ```
 */
export function AuthLayout({ children }: Readonly<AuthLayoutProps>): JSX.Element {
  return (
    <Grid
      container
      minHeight={'100%'}
      width={'100%'}
    >
      <Grid
        component={Paper}
        elevation={6}
        size={'grow'}
        square
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box py={{ xs: 2, sm: 3 }}>{children}</Box>
      </Grid>
    </Grid>
  );
}

export default AuthLayout;
