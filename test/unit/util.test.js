import { beforeEach, describe, expect, it, vi } from "vitest";
import { MongoClient } from "mongodb";
import { VALIDATIONS, INDEXES } from "../../src/constant";
import { processLog, saveLog } from "../../src/util";

vi.mock('mongodb', () => {
  const MongoClient = vi.fn();
  MongoClient.prototype.close = vi.fn();
  MongoClient.prototype.db = vi.fn();
  return { MongoClient };
});

describe("processLog unit test", () => {

  const expectKey = "2025_03";

  it("should convert timestamp from string to date type", async () => {

    const rawAuditLogString = '{"timestamp":"2025-03-01T12:34:56Z","details":{"request":{}}}';

    let auditLog = JSON.parse(rawAuditLogString);
    auditLog.timestamp = new Date(auditLog.timestamp);

    const response = await processLog(rawAuditLogString);
    expect(response).toEqual({ key: expectKey, value: auditLog })
  });
});

describe("saveLog unit test", () => {
  let auditTrailsByMonth = {};
  let existCollections = [];
  let client;
  const dbMock = vi.mocked(MongoClient.prototype.db);
  const bulkWriteMock = vi.fn();
  const options = { dbName: "qply-db", retryAttempt: 2 };

  beforeEach(() => {
    vi.resetAllMocks();
    client = new MongoClient();
    existCollections = ["audit_trails_2025_03", "audit_trails_2025_04"];

    auditTrailsByMonth = {};
    auditTrailsByMonth["2025_03"] = [{
      "timestamp": "2025-03-01T12:34:56Z",
      "details": {
        "request": {
          "requestId": "1"
        }
      }
    }, {
      "timestamp": "2025-03-01T12:34:56Z",
      "details": {
        "request": {
          "requestId": "1.1"
        }
      }
    }];
    auditTrailsByMonth["2025_04"] = [{
      "timestamp": "2025-04-01T12:34:56Z",
      "details": {
        "request": {
          "requestId": "2"
        }
      }
    }];

    dbMock.mockReturnValue({
      listCollections: vi.fn().mockImplementation((value) => {
        const collections = existCollections.filter((collectionName) => value.name === collectionName);
        return {
          toArray: vi.fn().mockReturnValue(collections)
        }
      }),
      createCollection: vi.fn(),
      command: vi.fn(),
      collection: vi.fn().mockReturnValue({
        createIndexes: vi.fn(),
        bulkWrite: bulkWriteMock,
      }),
    });
  });

  it("should not creating collection when have already have existing collection", async () => {

    await saveLog(auditTrailsByMonth, client);

    const dbInstance = MongoClient.mock.results[0].value.db();
    const collectionInstance = MongoClient.mock.results[0].value.db().collection();

    // Make sure no create collection related function is called
    expect(dbInstance.createCollection).not.toHaveBeenCalled();
    expect(dbInstance.command).not.toHaveBeenCalled();
    expect(collectionInstance.createIndexes).not.toHaveBeenCalled();
  })

  it("should have creating ONE collection only when one collection is already exist", async () => {
    // Mock only ONE collection is already exist
    existCollections = ["audit_trails_2025_03"];

    await saveLog(auditTrailsByMonth, client, options);

    const createCollectionName = 'audit_trails_2025_04'
    const createShardOptions = {
      shardCollection: `${options.dbName}.${createCollectionName}`,
      key: { entityAccessId: "hashed" },
    }

    const dbInstance = MongoClient.mock.results[0].value.db();
    const collectionInstance = MongoClient.mock.results[0].value.db().collection();

    // Make sure correct DB is connected
    expect(dbMock).toHaveBeenCalledWith(options.dbName);
    expect(dbMock).toHaveBeenCalledWith('admin');

    // Make sure only created ONE collection
    expect(dbInstance.createCollection).toHaveBeenCalledTimes(1);

    // Make sure create collection related function is called with correct parameters
    expect(dbInstance.createCollection).toHaveBeenCalledWith(createCollectionName, VALIDATIONS);
    expect(dbInstance.command).toHaveBeenCalledWith(createShardOptions);
    expect(collectionInstance.createIndexes).toHaveBeenCalledWith(INDEXES);
  })

  it("should be creating TWO collection when no existing collections", async () => {
    // Mock there are no existing collections
    const expectedNthTimes = existCollections.length;
    existCollections = [];

    await saveLog(auditTrailsByMonth, client, options);

    const dbInstance = MongoClient.mock.results[0].value.db();
    const collectionInstance = MongoClient.mock.results[0].value.db().collection();

    // Make sure correct DB is connected
    expect(dbMock).toHaveBeenCalledWith(options.dbName);
    expect(dbMock).toHaveBeenCalledWith('admin');

    // Make sure only created N collection
    expect(dbInstance.createCollection).toHaveBeenCalledTimes(expectedNthTimes);

    for (let i = 1; i <= expectedNthTimes; i++) {

      const createCollectionName = `audit_trails_2025_0${i + 2}`;
      const createShardOptions = {
        shardCollection: `${options.dbName}.${createCollectionName}`,
        key: { entityAccessId: "hashed" },
      }

      // Make sure create collection related function is called with correct parameters
      expect(dbInstance.createCollection).toHaveBeenNthCalledWith(i, createCollectionName, VALIDATIONS);
      expect(dbInstance.command).toHaveBeenNthCalledWith(i, createShardOptions);
      expect(collectionInstance.createIndexes).toHaveBeenNthCalledWith(i, INDEXES);
    }
  })

  it("should use bulk write to insert record", async () => {

    // Only need 1 row for easier testing
    delete auditTrailsByMonth['2025_03'];
    const expectInsertLog = auditTrailsByMonth['2025_04'][0];

    await saveLog(auditTrailsByMonth, client);

    const collectionInstance = MongoClient.mock.results[0].value.db().collection();

    // Make sure bulk write is called with correct parameters
    expect(collectionInstance.bulkWrite).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          updateOne: expect.objectContaining({
            filter: expect.objectContaining({
              "details.request.requestId": expectInsertLog.details.request.requestId
            }),
            update: expect.objectContaining({
              "$set": expectInsertLog
            }),
            upsert: true
          })
        })
      ])
    );
  });

  it("should retry when there are error", async () => {

    // Only need 1 row for easier testing
    delete auditTrailsByMonth['2025_03'];

    // Mock error
    const mockError = new Error('Mock error');
    let error = true;
    bulkWriteMock.mockImplementation(() => {
      if (error) {
        error = false
        throw mockError;
      }
    })

    await saveLog(auditTrailsByMonth, client);

    const collectionInstance = MongoClient.mock.results[0].value.db().collection();

    // Make bulkWrite is called twice (1 fail + 1 success)
    expect(collectionInstance.bulkWrite).toHaveBeenCalledTimes(options.retryAttempt);

  });

  it("should throw error after retry limit exceed", async () => {

    // Only need 1 row for easier testing
    delete auditTrailsByMonth['2025_03'];

    // Mock error
    bulkWriteMock.mockImplementation(() => {
      throw new Error("");
    });

    const expectedError = new Error("Failed to process log after retry");

    // Expect to throw error after retries
    await expect(saveLog(auditTrailsByMonth, client, options)).rejects.toThrow(expectedError);

    // TODO: check if has send notification. pending notification TSD
  });
});