import * as jwtUtil from '#src/utils/jwt.util.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { validate as uuidValidate, version as uuidVersion } from 'uuid';
import Fastify from 'fastify';
import onRequestHook from '#src/hooks/onRequest.hook.js';

describe('Test case for onRequest hook', () => {
  let fastify;
  let onRequestFunction;

  // Mock the data
  let mockRequest = {};
  let mockReply = {};

  vi.mock('i18next', () => ({
    default: {
      changeLanguage: vi.fn(),
      options: {
        fallbackLng: 'en',
      },
    },
  }));

  vi.mock('geoip-lite', () => ({
    default: {
      lookup: vi.fn(() => ({
        city: 'Test City',
        country: 'TC',
      })),
    },
  }));

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Reset the entire request object
    mockRequest = {
      headers: {
        'accept-language': 'en-MY',
      },
      server: {
        redis: {},
      },
      raw: {
        url: '/api-route',
      },
    };

    mockReply = {};
    // Register the hook
    await fastify.register(onRequestHook, {});

    // Extract the function
    onRequestFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onRequest')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onRequest hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onRequest', expect.any(Function));
  });

  it('should add startTime and uuid to the request object', async () => {
    // Trigger the onRequest hook
    await onRequestFunction(mockRequest, mockReply);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onRequest hook');

    // Verify start time
    expect(mockRequest.startTime).toBeDefined();
    expect(typeof mockRequest.startTime).toBe('number');

    // Verify UUID
    expect(mockRequest.id).toBeDefined();
    expect(typeof mockRequest.id).toBe('string');
    expect(uuidValidate(mockRequest.id) && uuidVersion(mockRequest.id) === 4).toBeTruthy();
  });

  it('should extract the correct language from accept-language header', async () => {
    // Test with a specific language
    mockRequest.headers['accept-language'] = 'fr-FR';
    await onRequestFunction(mockRequest, mockReply);
    expect(mockRequest.locale).toBe('fr-FR');

    // Test with fallback language
    mockRequest.headers['accept-language'] = undefined;
    await onRequestFunction(mockRequest, mockReply);
    expect(mockRequest.locale).toBeUndefined();
  });

  it('should attach decoded user to request if valid token provided', async () => {
    const mockDecodedTokenInfo = {
      basicInformation: {
        authInfo: {
          id: 'user-123',
          authAccess: 'user',
          username: 'user',
          department: 'user department',
          fingerprintId: 'fingerprint-123',
          role: 'user role',
        },
      },
    };
    vi.spyOn(jwtUtil, 'decodeJWT').mockResolvedValue(mockDecodedTokenInfo);

    mockRequest.headers = { authorization: 'Bearer valid.jwt.token' };

    await onRequestFunction(mockRequest, mockReply);

    expect(jwtUtil.decodeJWT).toHaveBeenCalledWith('valid.jwt.token', expect.any(Object));
    expect(mockRequest.authInfo).toEqual(mockDecodedTokenInfo.basicInformation.authInfo);
  });

  it('should not attach user if no authorization token is provided', async () => {
    const decodeSpy = vi.spyOn(jwtUtil, 'decodeJWT');

    mockRequest.headers = {};
    await onRequestFunction(mockRequest, mockReply);

    expect(decodeSpy).not.toHaveBeenCalled();
    expect(mockRequest.authInfo).toBeUndefined();
  });

  it('should handle JWT decoding errors gracefully', async () => {
    vi.spyOn(jwtUtil, 'decodeJWT').mockImplementation(() => {
      throw new Error('Invalid token signature');
    });

    mockRequest.headers = { authorization: 'Bearer invalid.jwt.token' };

    await onRequestFunction(mockRequest, mockReply);

    expect(jwtUtil.decodeJWT).toHaveBeenCalledWith('invalid.jwt.token', expect.any(Object));
    expect(mockRequest.authInfo.authAccess).toBeUndefined();
    expect(mockRequest.authInfo.id).toBeUndefined();
  });

  it('should attach entity info to request if present in decoded token', async () => {
    const mockDecodedTokenInfo = {
      basicInformation: {
        entity: {
          id: 'entity-456',
          accessLevel: 'admin',
        },
        authInfo: {
          id: 'user-123',
          authAccess: 'user',
          username: 'user',
          department: 'user department',
          fingerprintId: 'fingerprint-123',
          role: 'user role',
        },
      },
    };

    vi.spyOn(jwtUtil, 'decodeJWT').mockResolvedValue(mockDecodedTokenInfo);

    mockRequest.headers = { authorization: 'Bearer valid.jwt.token' };

    await onRequestFunction(mockRequest, mockReply);

    expect(jwtUtil.decodeJWT).toHaveBeenCalledWith('valid.jwt.token', expect.any(Object));

    expect(mockRequest.entity).toEqual({
      id: 'entity-456',
      accessLevel: 'admin',
    });

    expect(mockRequest.authInfo).toEqual({
      id: 'user-123',
      authAccess: 'user',
      username: 'user',
      department: 'user department',
      fingerprintId: 'fingerprint-123',
      role: 'user role',
    });
  });
});
