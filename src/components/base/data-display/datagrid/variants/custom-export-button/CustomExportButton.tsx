import { getFormattedFileName } from '@/components/base/data-display/datagrid/DataGrid.util';
import { Button } from '@/components/base/inputs/button';
import { Typography } from '@/components/base/typography';
import { transformText } from '@/utils/text-transform.util';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import {
  gridExpandedSortedRowIdsSelector,
  gridPaginatedVisibleSortedGridRowIdsSelector,
  gridSortedRowIdsSelector,
  ToolbarButton,
  useGridApiContext,
  type GridCsvExportOptions,
  type GridCsvGetRowsToExportParams,
} from '@mui/x-data-grid-premium';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Get rows based on the selected export type.
 */
const exportTypes = {
  current_page: (params: GridCsvGetRowsToExportParams) =>
    gridPaginatedVisibleSortedGridRowIdsSelector(params.apiRef),
  all: (params: GridCsvGetRowsToExportParams) => gridSortedRowIdsSelector(params.apiRef),
  filtered: (params: GridCsvGetRowsToExportParams) =>
    gridExpandedSortedRowIdsSelector(params.apiRef),
};

/**
 * Export for unit testing purposes.
 */
export const exportForUnitTest = {
  exportTypes,
};

/**
 * CustomExportButton component provides CSV export functionality for DataGrid
 * with options to export current page, all data, or filtered data
 */
const CustomExportButton = ({ csvOptions }: { csvOptions: GridCsvExportOptions }) => {
  const { t } = useTranslation();

  const apiRef = useGridApiContext();

  const [exportMenuOpen, setExportMenuOpen] = useState(false);
  const exportMenuTriggerRef = useRef<HTMLButtonElement>(null);

  const handleExport = (type: keyof typeof exportTypes) => {
    const csvExportOptions: GridCsvExportOptions = {
      ...csvOptions,
      utf8WithBom: true,
      fileName: getFormattedFileName(csvOptions?.fileName ?? 'export', type),
      getRowsToExport: exportTypes[type],
    };
    apiRef.current.exportDataAsCsv(csvExportOptions);
    setExportMenuOpen(false);
  };

  return (
    <>
      <ToolbarButton
        ref={exportMenuTriggerRef}
        id="export-menu-trigger"
        aria-controls="export-menu"
        aria-haspopup="true"
        aria-expanded={exportMenuOpen ? 'true' : undefined}
        onClick={() => setExportMenuOpen(true)}
        render={
          <Button
            arrow={true}
            color="primary"
            title={t('common.label.selectExport')}
            size="small"
            tooltipProps={{
              placement: 'bottom',
            }}
            variant="text"
            startIcon={<FileDownloadIcon />}
          >
            {t('common.button.export')}
          </Button>
        }
      />
      <Menu
        id="export-menu"
        anchorEl={exportMenuTriggerRef.current}
        open={exportMenuOpen}
        onClose={() => setExportMenuOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        slotProps={{ list: { 'aria-labelledby': 'export-menu-trigger' } }}
      >
        {Object.keys(exportTypes).map((type) => (
          <MenuItem
            key={type}
            onClick={() => handleExport(type as keyof typeof exportTypes)}
          >
            <Typography caseTransform="sentenceCase">
              {t(`common.label.export${transformText(type.replace('_', ' '), 'pascalCase')}`)}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default CustomExportButton;
