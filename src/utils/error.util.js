import createError from '@fastify/error';

/**
 * Utility function to create an extended error class with additional details.
 * @param {string} code - The error code.
 * @param {string} message - The error message template.
 * @param {number} statusCode - The HTTP status code.
 * @returns {Function} A constructor function for the custom error.
 */
const createCustomError = (code, message, moduleName, statusCode = 500) => {
  const BaseError = createError(code, message, statusCode);

  return class CustomError extends BaseError {
    /**
     * Constructs a new CustomError with additional details.
     * @param {...any} args - Arguments for the error message template.
     * @param {Object} [metaData] - Additional details about the error.
     */
    constructor(...args) {
      let extraDetails = {};
      if (args.length > 0 && typeof args[args.length - 1] === 'object') {
        extraDetails = args.pop();
      }
      super(...args);
      this.name = `${moduleName}ModuleError`;
      this.metaData = extraDetails;
    }
  };
};

/**
 * Generates module-scoped errors from a definition map.
 * @param {string} moduleName - The module name (e.g. 'Wallet', 'Localisation').
 * @param {Record<string, [string, string, number]>} errorDefs - Map of error key to [code, message, statusCode].
 * @returns {Record<string, (...params: any[]) => Error>} - Error generators.
 */
const createModuleErrors = (moduleName, errorDefs) => {
  return Object.fromEntries(
    Object.entries(errorDefs).map(([key, [code, message, statusCode]]) => {
      const ErrorClass = createCustomError(code, message, moduleName, statusCode);
      return [key, (...params) => new ErrorClass(...params)];
    }),
  );
};

/**
 * Creates a validation error that mimics Fastify's validation error structure
 *
 * @param {Array|Object} validationErrors - Array of validation errors or a single error object
 * @param {string} [message='Validation Error'] - Optional custom error message
 * @returns {Error} Error object with validation property
 */
const createValidationError = (validationErrors, message = 'Validation Error') => {
  // Ensure validationErrors is an array
  const errors = Array.isArray(validationErrors) ? validationErrors : [validationErrors];

  // Create an error object with the same structure Fastify uses
  const error = new Error(message);
  error.statusCode = 400;
  error.code = 'VALIDATION_ERROR';
  error.validation = errors;

  return error;
};

export { createCustomError, createModuleErrors, createValidationError };
