import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DatabaseFactory } from '#src/factories/database.factory.js';
import { autoloadModels } from '#src/utils/load-model.util.js';
import fp from 'fastify-plugin';

// Mock the external dependencies
vi.mock('#src/utils/load-model.util.js');
vi.mock('fastify-plugin');

describe('DatabaseFactory', () => {
  let mockFastify;

  beforeEach(() => {
    // Reset all mocks before each test
    vi.resetAllMocks();

    // Mock Fastify instance
    mockFastify = {
      decorate: vi.fn(),
    };

    // Mock fastify-plugin
    fp.mockImplementation((fn) => fn);
  });

  it('should create an instance with fastify', () => {
    const factory = new DatabaseFactory(mockFastify);
    expect(factory.fastify).toBe(mockFastify);
  });

  it('should throw an error when connect is called on base class', async () => {
    const factory = new DatabaseFactory(mockFastify);
    await expect(factory.connect()).rejects.toThrow(
      'connect method must be implemented by subclasses',
    );
  });

  it('should throw an error when decorate is called on base class', () => {
    const factory = new DatabaseFactory(mockFastify);
    expect(() => factory.decorate()).toThrow('decorate method must be implemented by subclasses');
  });

  describe('createPlugin', () => {
    it('should create a plugin that connects, decorates, and loads models', async () => {
      class TestDatabaseFactory extends DatabaseFactory {
        async connect() {}
        decorate() {}
      }

      const plugin = TestDatabaseFactory.createPlugin('test', true);
      await plugin(mockFastify);

      expect(autoloadModels).toHaveBeenCalledWith(mockFastify, 'test', null);
    });

    it('should not load models if loadModels is false', async () => {
      class TestDatabaseFactory extends DatabaseFactory {
        async connect() {}
        decorate() {}
      }

      const plugin = TestDatabaseFactory.createPlugin('test', false);
      await plugin(mockFastify);

      expect(autoloadModels).not.toHaveBeenCalled();
    });

    it('should call connect and decorate methods of subclass', async () => {
      const connectMock = vi.fn();
      const decorateMock = vi.fn();

      class TestDatabaseFactory extends DatabaseFactory {
        async connect() {
          connectMock();
        }
        decorate() {
          decorateMock();
        }
      }

      const plugin = TestDatabaseFactory.createPlugin('test');
      await plugin(mockFastify);

      expect(connectMock).toHaveBeenCalled();
      expect(decorateMock).toHaveBeenCalled();
    });

    it('should ensure each model has an associate function and call it with fastify.psql', async () => {
      const mockAssociate = vi.fn();
      const mockModelWithoutAssociate = {
        associate: vi.fn(), // define spy early
      };
      const mockModelWithAssociate = { associate: mockAssociate };

      mockFastify.psql = {
        model1: mockModelWithoutAssociate,
        model2: mockModelWithAssociate,
      };

      class TestDatabaseFactory extends DatabaseFactory {
        async connect() {}
        decorate() {}
      }

      const plugin = TestDatabaseFactory.createPlugin('postgres', true);
      await plugin(mockFastify);

      expect(typeof mockModelWithoutAssociate.associate).toBe('function');
      expect(mockAssociate).toHaveBeenCalledWith(mockFastify.psql);
      expect(mockModelWithoutAssociate.associate).toHaveBeenCalledWith(mockFastify.psql);
    });

    it('should correctly handle multiple read replicas from environment variables', () => {
      process.env.POSTGRES_READ_REPLICA_1_HOST = 'replica1_host';
      process.env.POSTGRES_READ_REPLICA_1_PORT = '5432';
      process.env.POSTGRES_READ_REPLICA_1_DB = 'replica1_db';
      process.env.POSTGRES_READ_REPLICA_1_USER = 'replica1_user';
      process.env.POSTGRES_READ_REPLICA_1_PASSWORD = 'replica1_password';

      process.env.POSTGRES_READ_REPLICA_2_HOST = 'replica2_host';
      process.env.POSTGRES_READ_REPLICA_2_PORT = '5433';
      process.env.POSTGRES_READ_REPLICA_2_DB = 'replica2_db';
      process.env.POSTGRES_READ_REPLICA_2_USER = 'replica2_user';
      process.env.POSTGRES_READ_REPLICA_2_PASSWORD = 'replica2_password';

      const factory = new DatabaseFactory(mockFastify);
      const replicas = factory.getReadReplicas();

      expect(replicas).toEqual([
        {
          host: 'replica1_host',
          port: '5432',
          database: 'replica1_db',
          username: 'replica1_user',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'replica1_password',
        },
        {
          host: 'replica2_host',
          port: '5433',
          database: 'replica2_db',
          username: 'replica2_user',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'replica2_password',
        },
      ]);
    });
  });
});
