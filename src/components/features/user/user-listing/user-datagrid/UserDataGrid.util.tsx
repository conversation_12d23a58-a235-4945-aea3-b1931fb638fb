import { stringAvatar } from '@/components/base/data-display/avatar';
import { createHeaderName } from '@/components/base/data-display/datagrid/DataGrid.util';
import { Label } from '@/components/base/data-display/label';
import { Typography } from '@/components/base/typography';
import { STATUS_LABEL_MAP } from '@/constants/status-labels.constant';
import transformText from '@/utils/text-transform.util';
import { Avatar, Box, Link, type Theme } from '@mui/material';
import {
  GridActionsCellItem,
  type GridColDef,
  type GridRenderCellParams,
  type GridRowParams,
} from '@mui/x-data-grid-premium';
import { DeleteUserActionItem } from './parts';

/**
 * Creates the column definitions for the UserDataGrid
 *
 * @param deleteUser - Function to delete a user by ID
 * @param theme - The current theme
 * @param t - Translation function
 * @returns Array of column definitions
 */
export const createColumns = (
  deleteUser: (id: string | number) => () => void,
  theme: Theme,
  t: (key: string) => string
): GridColDef[] => [
  {
    field: 'username',
    flex: 1,
    groupable: false,
    headerName: createHeaderName('username', t),
    hideable: false,
  },
  {
    field: 'name',
    flex: 1,
    groupable: false,
    headerName: createHeaderName('name', t),
    minWidth: 250,
    renderCell: (params: GridRenderCellParams) => (
      <Box
        display="flex"
        alignItems="center"
        sx={{
          width: '100%',
          overflow: 'hidden',
        }}
      >
        <Avatar
          variant="rounded"
          src={params.row.avatar}
          {...stringAvatar(params.row.username, theme, {
            width: 40,
            height: 40,
            flexShrink: 0,
          })}
        />
        <Box
          sx={{
            ml: 1,
            minWidth: 0,
            overflow: 'hidden',
          }}
        >
          <Link
            variant="subtitle1"
            fontWeight={500}
            href=""
            onClick={(e) => e.preventDefault()}
            underline="hover"
            sx={{
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {params.row.name}
          </Link>
          <Typography
            variant="subtitle2"
            color="text.secondary"
            sx={{
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {params.row.jobtitle}
          </Typography>
        </Box>
      </Box>
    ),
  },
  {
    field: 'email',
    flex: 1,
    groupable: false,
    headerName: createHeaderName('email', t),
    minWidth: 250,
  },
  {
    field: 'posts',
    flex: 0.5,
    groupable: false,
    headerName: createHeaderName('posts', t),
    type: 'number',
  },
  {
    field: 'location',
    flex: 1,
    headerName: createHeaderName('location', t),
  },
  {
    field: 'role',
    flex: 0.8,
    headerName: createHeaderName('role', t),
    renderCell: (params: GridRenderCellParams) => (
      <Box sx={{ textTransform: 'capitalize' }}>{params.value}</Box>
    ),
  },
  {
    field: 'status',
    flex: 0.8,
    headerName: createHeaderName('status', t),
    renderCell: (params: GridRenderCellParams) => (
      <Label
        value={params.row.status}
        map={STATUS_LABEL_MAP}
      />
    ),
  },
  {
    field: 'actions',
    description: createHeaderName('actions', t),
    flex: 0.1,
    hideable: false,
    type: 'actions',
    width: 40,
    getActions: (params: GridRowParams) => [
      <GridActionsCellItem
        key={params.id}
        label={transformText(t('view details'), 'sentenceCase')}
        showInMenu
      />,
      <DeleteUserActionItem
        key={params.id}
        label={transformText(t('delete'), 'sentenceCase')}
        showInMenu
        closeMenuOnClick={false}
        deleteUser={() => deleteUser(params.id)()}
      />,
    ],
  },
];
