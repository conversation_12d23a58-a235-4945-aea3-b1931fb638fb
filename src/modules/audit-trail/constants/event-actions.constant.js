const EVENT_ACTION_DESCRIPTIONS = {
  ARCHIVED: {
    name: (event) => `${event} Archived`,
    description: (username, event, referenceId) =>
      `System has completely archived the ${event} (ID: ${referenceId}).`,
  },
  CREATED: {
    name: (event) => `${event} Created`,
    description: (username, event) => `${username} created a new ${event}.`,
  },
  DEFAULT: {
    name: (event) => `${event} Action`,
    description: (username, event) => `${username} performed an action on ${event}.`,
  },
  DELETED: {
    name: (event) => `${event} Deleted`,
    description: (username, event, referenceId) =>
      `${username} deleted the ${event} (ID: ${referenceId}).`,
  },
  DUPLICATED: {
    name: (event) => `${event} Duplicated`,
    description: (username, event, referenceId) =>
      `System has completely duplicated the ${event} configuration (ID: ${referenceId}).`,
  },
  EDITED: {
    name: (event) => `${event} Edited`,
    description: (username, event, referenceId) =>
      `${username} edited the ${event} (ID: ${referenceId}).`,
  },
  EXPORTED: {
    name: (event) => `${event} Exported`,
    description: (username, event) => `${username} exported the ${event} list.`,
  },
  GENERATED_API_KEY: {
    name: (event) => `${event} API Key Generated`,
    description: (username, event) => `${username} generated a new ${event} API Key.`,
  },
  GENERATED_FORGOT_PASSWORD: {
    name: () => `Forgot Password Requested`,
    description: (username) => `${username} requested a password reset.`,
  },
  INSTALLED: {
    name: (event) => `${event} Installed`,
    description: (username, event, referenceId) =>
      `${username} installed an app (ID: ${referenceId}).`,
  },
  IP_BLACKLISTED: {
    name: () => `IP Blacklisted`,
    description: () => `A system rule automatically blacklisted an IP.`,
  },
  KILL_SWITCH_ACTIVATED: {
    name: () => `Kill Switch Activated`,
    description: (username, event, referenceId, hierarchy, entityName) =>
      `${username} activated the kill switch for ${hierarchy} ${entityName} (ID: ${referenceId}).`,
  },
  KILL_SWITCH_DEACTIVATED: {
    name: () => `Kill Switch Deactivated`,
    description: (username, event, referenceId, hierarchy, entityName) =>
      `${username} deactivated the kill switch for ${hierarchy} ${entityName} (ID: ${referenceId}).`,
  },
  LOGIN: {
    name: () => `Logged in account`,
    description: (username, event) => `${username} logged in.`,
  },
  LOGOUT: {
    name: () => `Logged out account`,
    description: (username) => `${username} logged out.`,
  },
  RAN_TEST: {
    name: (event) => `${event} Test Run`,
    description: (username, event, referenceId) =>
      `${username} ran a test for an app (ID: ${referenceId}).`,
  },
  REQUEST_OTP: {
    name: (event) => `${event} Requested`,
    description: (username, event) => `${username} requested an ${event}.`,
  },
  RESET_2FA: {
    name: () => `2FA Reset`,
    description: (username) => `${username} reset 2FA.`,
  },
  SEARCHED: {
    name: (event) => `${event} Searched`,
    description: (username, event) => `${username} searched the ${event}.`,
  },
  SENT_OTP: {
    name: (event) => `${event} Sent`,
    description: (username, event) => `System sent an ${event} to ${username}.`,
  },
  SETUP: {
    name: (event) => `${event} Setup`,
    description: (username, event, referenceId) =>
      `System has completed process the ${event} setup (ID: ${referenceId}).`,
  },
  SETUP_2FA: {
    name: () => `2FA Setup`,
    description: (username) => `${username} set up 2FA.`,
  },
  STATUS_UPDATED: {
    name: (event) => `${event} Status Updated`,
    description: (username, event, referenceId) =>
      `${username} updated the ${event} status (ID: ${referenceId}).`,
  },
  SUSPENDED_USER: {
    name: () => `User Suspended`,
    description: (username) => `${username} has been suspended.`,
  },
  UNDER_ATTACK_MODE_ACTIVATED: {
    name: () => `Under Attack Mode Activated`,
    description: (username, event, referenceId, hierarchy, entityName) =>
      `${username} activated the Under Attack Mode for ${hierarchy} ${entityName} (ID: ${referenceId}).`,
  },
  UNDER_ATTACK_MODE_DEACTIVATED: {
    name: () => `Under Attack Mode Deactivated`,
    description: (username, event, referenceId, hierarchy, entityName) =>
      `${username} deactivated the Under Attack Mode for ${hierarchy} ${entityName} (ID: ${referenceId}).`,
  },
  UNINSTALLED: {
    name: (event) => `${event} Uninstalled`,
    description: (username, event, referenceId) =>
      `${username} uninstalled an app (ID: ${referenceId}).`,
  },
  UPDATED: {
    name: (event) => `${event} Updated`,
    description: (username, event, referenceId) =>
      `${username} updated the ${event} (ID: ${referenceId}).`,
  },
  UPDATED_STATUS: {
    name: (event) => `${event} Status Updated`,
    description: (username, event, referenceId) =>
      `${username} updated the ${event} status (ID: ${referenceId}).`,
  },
  UPDATED_PASSWORD: {
    name: () => `Password Updated`,
    description: (username) => `${username} updated their password.`,
  },
  VERIFIED_OTP: {
    name: (event) => `${event} Verified`,
    description: (username, event, referenceId, hierarchy, entityName, status) =>
      `${event} verification for ${username} was ${status}.`,
  },
  VERIFIED_2FA: {
    name: () => `2FA Verified`,
    description: (username, event, referenceId, hierarchy, entityName, status) =>
      `2FA for ${username} was ${status}.`,
  },
  VIEWED: {
    name: (event) => `${event} Viewed`,
    description: (username, event) => `${username} viewed the ${event} list.`,
  },
  VIEWED_DETAILS: {
    name: (event) => `${event} Details Viewed`,
    description: (username, event, referenceId) =>
      `${username} viewed the ${event} details (ID: ${referenceId}).`,
  },
};

export { EVENT_ACTION_DESCRIPTIONS };
