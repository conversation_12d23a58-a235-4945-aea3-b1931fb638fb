import fp from 'fastify-plugin';
import i18next from 'i18next';

import { decodeJWT } from '#src/utils/jwt.util.js';
import { v4 as uuidv4 } from 'uuid';
/**
 * onRequest hook
 *
 * This hook is executed for every request before the routing is executed.
 * It can be used for logging, setting up request-specific variables, or performing
 * initial checks before processing the request.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onRequestHook = (fastify, options) => {
  fastify.addHook('onRequest', async (request, reply) => {
    fastify.log.debug('Executing onRequest hook');
    request.startTime = Date.now();
    request.id = uuidv4();

    // Parse the accept-language header
    const acceptLanguage = request.headers['accept-language'];
    let language = i18next.options.fallbackLng;

    if (acceptLanguage) {
      const languages = acceptLanguage.split(',').map((lang) => lang.split(';')[0]);
      language = languages[0].split('-')[0] || language;
    }
    await i18next.changeLanguage(language);

    // Decode JWT and attach user
    const token = request.headers.authorization?.replace('Bearer ', '');

    if (token) {
      try {
        const tokenInfo = await decodeJWT(token, fastify);

        request.entity = {
          id: tokenInfo?.basicInformation?.entity?.id,
          accessLevel: tokenInfo?.basicInformation?.entity?.accessLevel,
          hierarchy: tokenInfo?.basicInformation?.entity?.hierarchy,
          project_code: tokenInfo?.basicInformation?.entity?.project_code,
          prefix: tokenInfo?.basicInformation?.entity?.prefix,
          name: tokenInfo?.basicInformation?.entity?.name,
        };

        request.parentEntity = {
          id: tokenInfo?.basicInformation?.parentEntity?.id,
          hierarchy: tokenInfo?.basicInformation?.parentEntity?.hierarchy,
          project_code: tokenInfo?.basicInformation?.parentEntity?.project_code,
          prefix: tokenInfo?.basicInformation?.parentEntity?.prefix,
          name: tokenInfo?.basicInformation?.parentEntity?.name,
        };

        request.authInfo = {
          id: tokenInfo?.basicInformation?.authInfo?.id,
          authAccess: tokenInfo?.basicInformation?.authInfo?.authAccess,
          username: tokenInfo?.basicInformation?.authInfo?.username,
          role: tokenInfo?.basicInformation?.authInfo?.role,
          department: tokenInfo?.basicInformation?.authInfo?.department,
          fingerprintId: tokenInfo?.basicInformation?.authInfo?.fingerprintId,
        };
      } catch (error) {
        fastify.log.debug(error, 'Failed to decode JWT token');
        request.authInfo = {};
        request.entity = {};
      }
    }

    request.locale = acceptLanguage; // Store the detected locale in the request object
  });
};

export default fp(onRequestHook, {
  name: 'onRequestHook',
});
