# APP CONFIGURATION
APP_NAME=wlsapi
BASE_URL=http://wlsapi.loc:3000/
DOCKER_CONTAINER=true
DOCKER_PORT=3000
IMAGE_TAG=dev
LOG_LEVEL=trace
NODE_ENV=development

## AUTHENTICATION
JWT_ALGORITHM=HS256
JWT_AUDIENCE=quantumplay.com
JWT_ISSUER=QPLY 2.0
JWT_SECRET=6c62f5fe227f211a1da3b7b35a287a70242411a1244e3db43838f77f9524c0fe

## Fastify Server
FASTIFY_ADDRESS=0.0.0.0
FASTIFY_PORT=3000

# AWS CONFIGURATION
AWS_ACCESS_KEY_ID=
AWS_BUCKET=
AWS_REGION=
AWS_S3_PATH=
AWS_SECRET_ACCESS_KEY=

# DATABASE CONFIGURATION
## MongoDB
MONGO_DB=qply-db
MONGO_HOST=host.docker.internal
MONGO_PORT=27017
MONGO_ROOT_PASSWORD=s5l2t9o49
MONGO_ROOT_USERNAME=qply

## PostgreSQL
DB_DIALECT=postgres
POSTGRES_DB=qply-db
POSTGRES_HOST=host.docker.internal
POSTGRES_PASSWORD=s5l2t9o49
POSTGRES_PORT=5432
POSTGRES_USER=wlsapi

## Sequelize Postgres Read Replicas
POSTGRES_READ_REPLICA_1_HOST=host.docker.internal
POSTGRES_READ_REPLICA_1_USER=wlsapi
POSTGRES_READ_REPLICA_1_PASSWORD=s5l2t9o49

POSTGRES_READ_REPLICA_2_HOST=host.docker.internal
POSTGRES_READ_REPLICA_2_USER=wlsapi
POSTGRES_READ_REPLICA_2_PASSWORD=s5l2t9o49

# ELASTIC STACK CONFIGURATION
ELASTIC_STACK_VERSION=8.16.1

## Logstash
LOGSTASH_HOST=host.docker.internal
LOGSTASH_PORT=5044

# REDIS CONFIGURATION
REDIS_HOST=host.docker.internal
REDIS_PASSWORD=s5l2t9o49
REDIS_PORT=6379
REDIS_RATE_LIMIT_CONNECT_TIMEOUT=500

# SENTRY CONFIGURATION
SENTRY_DSN=

# SWAGGER API DOCUMENTATION
API_KEY_NAME=x-api-key
EXTERNAL_DOCS_DESCRIPTION=
EXTERNAL_DOCS_URL=
SERVER_URL_DEV=
SERVER_URL_PROD=
SERVER_URL_STAGING=
SWAGGER_UI_PATH=/api-docs

## Localisation configuration
## AWS CDN URL for Language Localisation System Translation
LLS_CDN_BASE_URL=https://006cb6bd.quickcdn.org
## Default fallback language for i18next
DEFAULT_LOCALE=en-MY

## TYPESENSE
TYPESENSE_API_KEY=72d6f083-e74d-4921-8bfe-eede33ffebbb
TYPESENSE_HOST=host.docker.internal
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http

## KAFKA
KAFKA=true
KAFKA_BROKERS=host.docker.internal:9094

## SYSTEM SETTING
BASE_CURRENCY=USD

## GEOIP
GEOIP_LICENSE_KEY=****************************************
