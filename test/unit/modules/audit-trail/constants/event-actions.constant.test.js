import { describe, expect, it } from 'vitest';
import { EVENT_ACTION_DESCRIPTIONS } from '#src/modules/audit-trail/constants/event-actions.constant.js';

describe('EVENT_ACTION_DESCRIPTIONS', () => {
  // Define common test data
  const MOCK_USERNAME = 'testUser';
  const MOCK_EVENT_NAME = 'UserAccount';
  const MOCK_REFERENCE_ID = '12345';
  const MOCK_HIERARCHY = 'organization';
  const MOCK_ENTITY_NAME = 'QuantumPlay Inc.';
  const MOCK_STATUS = 'successful';

  // Helper function to test a single event action
  const testEventAction = (
    actionKey,
    expectedName,
    expectedDescription,
    event,
    username,
    referenceId,
    hierarchy,
    entityName,
    status,
  ) => {
    const action = EVENT_ACTION_DESCRIPTIONS[actionKey];

    it(`should define name and description for ${actionKey}`, () => {
      expect(action).toBeDefined();
      expect(typeof action.name).toBe('function');
      expect(typeof action.description).toBe('function');
    });

    it(`should return correct name for ${actionKey}`, () => {
      if (event) {
        expect(action.name(event)).toBe(expectedName);
      } else {
        expect(action.name()).toBe(expectedName);
      }
    });

    it(`should return correct description for ${actionKey}`, () => {
      // Use consistent arguments for description function call
      expect(action.description(username, event, referenceId, hierarchy, entityName, status)).toBe(
        expectedDescription,
      );
    });
  };

  // --- Test each event action ---

  testEventAction(
    'ARCHIVED',
    `${MOCK_EVENT_NAME} Archived`,
    `System has completely archived the ${MOCK_EVENT_NAME} (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'CREATED',
    `${MOCK_EVENT_NAME} Created`,
    `${MOCK_USERNAME} created a new ${MOCK_EVENT_NAME}.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
  );

  testEventAction(
    'DEFAULT',
    `${MOCK_EVENT_NAME} Action`,
    `${MOCK_USERNAME} performed an action on ${MOCK_EVENT_NAME}.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
  );

  testEventAction(
    'DELETED',
    `${MOCK_EVENT_NAME} Deleted`,
    `${MOCK_USERNAME} deleted the ${MOCK_EVENT_NAME} (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'DUPLICATED',
    `${MOCK_EVENT_NAME} Duplicated`,
    `System has completely duplicated the ${MOCK_EVENT_NAME} configuration (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'EDITED',
    `${MOCK_EVENT_NAME} Edited`,
    `${MOCK_USERNAME} edited the ${MOCK_EVENT_NAME} (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'EXPORTED',
    `${MOCK_EVENT_NAME} Exported`,
    `${MOCK_USERNAME} exported the ${MOCK_EVENT_NAME} list.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
  );

  testEventAction(
    'GENERATED_API_KEY',
    `${MOCK_EVENT_NAME} API Key Generated`,
    `${MOCK_USERNAME} generated a new ${MOCK_EVENT_NAME} API Key.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
  );

  testEventAction(
    'GENERATED_FORGOT_PASSWORD',
    `Forgot Password Requested`,
    `${MOCK_USERNAME} requested a password reset.`,
    undefined,
    MOCK_USERNAME,
  );

  testEventAction(
    'INSTALLED',
    `${MOCK_EVENT_NAME} Installed`,
    `${MOCK_USERNAME} installed an app (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'IP_BLACKLISTED',
    `IP Blacklisted`,
    `A system rule automatically blacklisted an IP.`,
    undefined,
    undefined,
  );

  testEventAction(
    'KILL_SWITCH_ACTIVATED',
    `Kill Switch Activated`,
    `${MOCK_USERNAME} activated the kill switch for ${MOCK_HIERARCHY} ${MOCK_ENTITY_NAME} (ID: ${MOCK_REFERENCE_ID}).`,
    undefined,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
    MOCK_HIERARCHY,
    MOCK_ENTITY_NAME,
  );

  testEventAction(
    'KILL_SWITCH_DEACTIVATED',
    `Kill Switch Deactivated`,
    `${MOCK_USERNAME} deactivated the kill switch for ${MOCK_HIERARCHY} ${MOCK_ENTITY_NAME} (ID: ${MOCK_REFERENCE_ID}).`,
    undefined,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
    MOCK_HIERARCHY,
    MOCK_ENTITY_NAME,
  );

  testEventAction(
    'LOGIN',
    `Logged in account`,
    `${MOCK_USERNAME} logged in.`,
    undefined,
    MOCK_USERNAME,
  );

  testEventAction(
    'LOGOUT',
    `Logged out account`,
    `${MOCK_USERNAME} logged out.`,
    undefined,
    MOCK_USERNAME,
  );

  testEventAction(
    'RAN_TEST',
    `${MOCK_EVENT_NAME} Test Run`,
    `${MOCK_USERNAME} ran a test for an app (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'REQUEST_OTP',
    `${MOCK_EVENT_NAME} Requested`,
    `${MOCK_USERNAME} requested an ${MOCK_EVENT_NAME}.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
  );

  testEventAction(
    'RESET_2FA',
    `2FA Reset`,
    `${MOCK_USERNAME} reset 2FA.`,
    undefined,
    MOCK_USERNAME,
  );

  testEventAction(
    'SEARCHED',
    `${MOCK_EVENT_NAME} Searched`,
    `${MOCK_USERNAME} searched the ${MOCK_EVENT_NAME}.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
  );

  testEventAction(
    'SENT_OTP',
    `${MOCK_EVENT_NAME} Sent`,
    `System sent an ${MOCK_EVENT_NAME} to ${MOCK_USERNAME}.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
  );

  testEventAction(
    'SETUP',
    `${MOCK_EVENT_NAME} Setup`,
    `System has completed process the ${MOCK_EVENT_NAME} setup (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'SETUP_2FA',
    `2FA Setup`,
    `${MOCK_USERNAME} set up 2FA.`,
    undefined,
    MOCK_USERNAME,
  );

  testEventAction(
    'STATUS_UPDATED',
    `${MOCK_EVENT_NAME} Status Updated`,
    `${MOCK_USERNAME} updated the ${MOCK_EVENT_NAME} status (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'SUSPENDED_USER',
    `User Suspended`,
    `${MOCK_USERNAME} has been suspended.`,
    undefined,
    MOCK_USERNAME,
  );

  testEventAction(
    'UNDER_ATTACK_MODE_ACTIVATED',
    `Under Attack Mode Activated`,
    `${MOCK_USERNAME} activated the Under Attack Mode for ${MOCK_HIERARCHY} ${MOCK_ENTITY_NAME} (ID: ${MOCK_REFERENCE_ID}).`,
    undefined,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
    MOCK_HIERARCHY,
    MOCK_ENTITY_NAME,
  );

  testEventAction(
    'UNDER_ATTACK_MODE_DEACTIVATED',
    `Under Attack Mode Deactivated`,
    `${MOCK_USERNAME} deactivated the Under Attack Mode for ${MOCK_HIERARCHY} ${MOCK_ENTITY_NAME} (ID: ${MOCK_REFERENCE_ID}).`,
    undefined,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
    MOCK_HIERARCHY,
    MOCK_ENTITY_NAME,
  );

  testEventAction(
    'UNINSTALLED',
    `${MOCK_EVENT_NAME} Uninstalled`,
    `${MOCK_USERNAME} uninstalled an app (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'UPDATED',
    `${MOCK_EVENT_NAME} Updated`,
    `${MOCK_USERNAME} updated the ${MOCK_EVENT_NAME} (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  testEventAction(
    'UPDATED_PASSWORD',
    `Password Updated`,
    `${MOCK_USERNAME} updated their password.`,
    undefined,
    MOCK_USERNAME,
  );

  testEventAction(
    'VERIFIED_OTP',
    `${MOCK_EVENT_NAME} Verified`,
    `${MOCK_EVENT_NAME} verification for ${MOCK_USERNAME} was ${MOCK_STATUS}.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
    MOCK_HIERARCHY,
    MOCK_ENTITY_NAME,
    MOCK_STATUS,
  );

  testEventAction(
    'VERIFIED_2FA',
    `2FA Verified`,
    `2FA for ${MOCK_USERNAME} was ${MOCK_STATUS}.`,
    undefined,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
    MOCK_HIERARCHY,
    MOCK_ENTITY_NAME,
    MOCK_STATUS,
  );

  testEventAction(
    'VIEWED',
    `${MOCK_EVENT_NAME} Viewed`,
    `${MOCK_USERNAME} viewed the ${MOCK_EVENT_NAME} list.`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
  );

  testEventAction(
    'VIEWED_DETAILS',
    `${MOCK_EVENT_NAME} Details Viewed`,
    `${MOCK_USERNAME} viewed the ${MOCK_EVENT_NAME} details (ID: ${MOCK_REFERENCE_ID}).`,
    MOCK_EVENT_NAME,
    MOCK_USERNAME,
    MOCK_REFERENCE_ID,
  );

  it('should fall back to DEFAULT for unknown action keys', () => {
    const unknownAction = EVENT_ACTION_DESCRIPTIONS.NON_EXISTENT_ACTION;
    expect(unknownAction).toBeUndefined();

    const defaultAction = EVENT_ACTION_DESCRIPTIONS.DEFAULT;
    expect(defaultAction.name(MOCK_EVENT_NAME)).toBe(`${MOCK_EVENT_NAME} Action`);
    expect(defaultAction.description(MOCK_USERNAME, MOCK_EVENT_NAME)).toBe(
      `${MOCK_USERNAME} performed an action on ${MOCK_EVENT_NAME}.`,
    );
  });
});
