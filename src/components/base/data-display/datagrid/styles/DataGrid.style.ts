import { styled } from '@mui/material';
import { DataGridPremium } from '@mui/x-data-grid-premium';

/**
 * A styled DataGridPremium component with custom styling.
 */
export const DataGridWrapper = styled(DataGridPremium)(({ theme }) => ({
  border: 'none',
  height: '100%', // Make sure the grid takes full height
  '& .MuiDataGrid-main': {
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    border: `1px solid ${theme.palette.divider}`,
    overflow: 'hidden',
    boxShadow: theme.shadows[8],
    // Enable smooth scrolling
    '& .MuiDataGrid-virtualScroller': {
      overflow: 'auto',
      '&::-webkit-scrollbar': {
        width: '8px',
        height: '8px',
      },
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.palette.divider,
        borderRadius: '4px',
      },
      '&::-webkit-scrollbar-track': {
        backgroundColor: theme.palette.background.paper,
      },
    },
  },
  // Fixed column headers
  '& .MuiDataGrid-columnHeaders': {
    position: 'sticky',
    top: 0,
    zIndex: 2,
    '& .MuiDataGrid-columnHeader': {
      // Remove focus outline
      '&:focus, &:focus-within': {
        outline: 'none',
      },
    },
  },
  // Row styling
  '& .MuiDataGrid-row': {
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
    // Align content vertically
    '& .MuiDataGrid-cell': {
      display: 'flex',
      alignItems: 'center',
      padding: theme.spacing(1),
      borderBottom: `1px solid ${theme.palette.divider}`,
      fontWeight: theme.typography.fontWeightRegular,
    },
  },
  // Footer / Pagination styling
  '& .MuiDataGrid-footerContainer': {
    border: 'none',
    marginTop: theme.spacing(1),
    position: 'sticky',
    bottom: 0,
    zIndex: 2,
    backgroundColor: theme.palette.background.paper,
    '& .MuiTablePagination-root': {
      marginRight: theme.spacing(1),
    },
    '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
      marginBottom: 0,
    },
    '& .MuiTablePagination-select': {
      marginRight: theme.spacing(2),
      marginLeft: theme.spacing(1),
    },
  },
  // Toolbar styling
  '& .MuiDataGrid-toolbar': {
    padding: theme.spacing(1, 0),
    gap: theme.spacing(1),
    backgroundColor: theme.palette.background.paper,
  },
  // Remove cell focus outline
  '& .MuiDataGrid-cell:focus, & .MuiDataGrid-cell:focus-within': {
    outline: 'none',
  },
  // Selected row styling
  '& .MuiDataGrid-row.Mui-selected': {
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
}));
