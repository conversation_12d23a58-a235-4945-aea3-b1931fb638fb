import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  clearAllCache,
  clearCache,
  clearCacheWithPrefix,
  fetchFromCache,
  getCache,
  getCacheKeysWithPrefix,
  setCache,
} from '#src/utils/cache.util.js';

describe('Cache Utility', () => {
  let mockRedis;

  beforeEach(() => {
    mockRedis = {
      get: vi.fn(),
      set: vi.fn(),
      setex: vi.fn(),
      del: vi.fn(),
      keys: vi.fn(),
      flushall: vi.fn(),
    };
  });

  describe('fetchFromCache', () => {
    it('should return cached data if it exists', async () => {
      const cacheKey = 'test_key';
      const cachedData = JSON.stringify({ foo: 'bar' });
      mockRedis.get.mockResolvedValue(cachedData);

      const result = await fetchFromCache(mockRedis, cacheKey, () => {});

      expect(result).toEqual({ foo: 'bar' });
      expect(mockRedis.get).toHaveBeenCalledWith(cacheKey);
    });

    it('should call callback and cache result if data is not in cache', async () => {
      const cacheKey = 'test_key';
      const newData = { foo: 'baz' };
      mockRedis.get.mockResolvedValue(null);
      const callback = vi.fn().mockResolvedValue(newData);

      const result = await fetchFromCache(mockRedis, cacheKey, callback);

      expect(result).toEqual(newData);
      expect(callback).toHaveBeenCalled();
      expect(mockRedis.setex).toHaveBeenCalledWith(cacheKey, 86400, JSON.stringify(newData));
    });

    it('should throw an error for invalid key', async () => {
      await expect(fetchFromCache(mockRedis, '', () => {})).rejects.toThrow(
        'Cache key must be a non-empty string',
      );
    });
  });

  describe('getCache', () => {
    it('should return cached data if it exists', async () => {
      const cacheKey = 'test_key';
      const cachedData = JSON.stringify({ foo: 'bar' });
      mockRedis.get.mockResolvedValue(cachedData);

      const result = await getCache(mockRedis, cacheKey);

      expect(result).toEqual({ foo: 'bar' });
      expect(mockRedis.get).toHaveBeenCalledWith(cacheKey);
    });

    it('should return null if data is not in cache', async () => {
      const cacheKey = 'test_key';
      mockRedis.get.mockResolvedValue(null);

      const result = await getCache(mockRedis, cacheKey);

      expect(result).toBeNull();
    });

    it('should throw an error for invalid key', async () => {
      await expect(getCache(mockRedis, '')).rejects.toThrow('Cache key must be a non-empty string');
    });
  });

  describe('setCache', () => {
    it('should set cache with correct parameters', async () => {
      const cacheKey = 'test_key';
      const value = { foo: 'bar' };
      const expiration = 3600;

      await setCache(mockRedis, cacheKey, value, expiration);

      expect(mockRedis.setex).toHaveBeenCalledWith(cacheKey, expiration, JSON.stringify(value));
    });

    it('should use default expiration if not provided', async () => {
      const cacheKey = 'test_key';
      const value = { foo: 'bar' };

      await setCache(mockRedis, cacheKey, value);
      expect(mockRedis.setex).toHaveBeenCalledWith(cacheKey, 86400, JSON.stringify(value));
    });
  });

  describe('clearCache', () => {
    it('should clear cache for a specific key', async () => {
      const cacheKey = 'test_key';

      await clearCache(mockRedis, cacheKey);
      expect(mockRedis.del).toHaveBeenCalledWith(cacheKey);
    });
  });

  describe('clearCacheWithPrefix', () => {
    it('should clear cache with a specific prefix', async () => {
      const prefix = 'test_';
      const keys = ['test_1', 'test_2'];
      mockRedis.keys.mockResolvedValue(keys);

      await clearCacheWithPrefix(mockRedis, prefix);

      expect(mockRedis.keys).toHaveBeenCalledWith(`${prefix}*`);
      expect(mockRedis.del).toHaveBeenCalledWith(keys);
    });
  });

  describe('clearAllCache', () => {
    it('should clear all cache', async () => {
      await clearAllCache(mockRedis);

      expect(mockRedis.flushall).toHaveBeenCalled();
    });
  });

  describe('getCacheKeysWithPrefix', () => {
    it('should return keys matching the prefix', async () => {
      const prefix = 'test_';
      const keys = ['test_1', 'test_2', 'test_3'];
      mockRedis.keys.mockResolvedValue(keys);

      const result = await getCacheKeysWithPrefix(mockRedis, prefix);

      expect(mockRedis.keys).toHaveBeenCalledWith(`${prefix}*`);
      expect(result).toEqual(keys);
    });

    it('should return null if no keys found', async () => {
      const prefix = 'no_match_';
      mockRedis.keys.mockResolvedValue(null);

      const result = await getCacheKeysWithPrefix(mockRedis, prefix);

      expect(mockRedis.keys).toHaveBeenCalledWith(`${prefix}*`);
      expect(result).toBeNull();
    });

    it('should return null if keys is an empty array', async () => {
      const prefix = 'empty_';
      mockRedis.keys.mockResolvedValue([]);

      const result = await getCacheKeysWithPrefix(mockRedis, prefix);

      expect(result).toEqual([]);
    });
  });
});
