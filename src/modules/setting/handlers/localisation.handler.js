import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';
import { setAuditMeta } from '#src/utils/audit-trail.util.js';

const {
  CACHE_SECOND: { SHORT },
  EVENTS: { SYSTEM_LOCALISATION_SETTINGS },
  EVENT_ACTIONS: { EDITED, SEARCHED, UPDATED_STATUS, VIEWED_DETAILS },
  MODULE_NAMES: { LOCALISATION },
  MODULE_METHODS: { INDEX, VIEW, UPDATE, UPDATE_STATUS },
} = CoreConstant;

const MODULE = LOCALISATION;

/**
 * Handles the retrieval of localisation data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the localisation data and pagination details.
 */
export const index = async (request, reply) => {
  setAuditMeta(request, {
    module: MODULE,
    event: SYSTEM_LOCALISATION_SETTINGS,
    action: SEARCHED,
  });

  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => LocalisationService.index(request), SHORT);

  await request.server.withAuditLogging({
    request,
  });

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Retrieves a specific localisation entry by ID.
 *
 * @param {Object} request - The request object containing the ID parameter.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the localisation entry.
 */
export const view = async (request, reply) => {
  const { id } = request.params;

  setAuditMeta(
    request,
    {
      module: MODULE,
      event: SYSTEM_LOCALISATION_SETTINGS,
      action: VIEWED_DETAILS,
    },
    id,
  );
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);

  const result = await fetchFromCache(
    request.server.redis,
    cacheKey,
    () => LocalisationService.view(request, id),
    SHORT,
  );

  await request.server.withAuditLogging({
    request,
    modelMapping: {
      Localisation: {
        beforeState: result,
      },
    },
  });

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Updates a localisation entry.
 *
 * @param {Object} request - The request object containing the ID parameter and the updated data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated localisation entry.
 */
export const update = async (request, reply) => {
  const { id } = request.params;

  setAuditMeta(
    request,
    {
      module: MODULE,
      event: SYSTEM_LOCALISATION_SETTINGS,
      action: EDITED,
    },
    id,
  );

  const { result, audit } = await LocalisationService.update(request, id);

  await request.server.withAuditLogging({
    request,
    modelMapping: {
      Localisation: audit,
    },
  });

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: UPDATE,
  });
};

/**
 * Updates the status of a localisation entry.
 *
 * @param {Object} request - The request object containing the ID parameter and the updated status.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated localisation entry.
 */
export const updateStatus = async (request, reply) => {
  const { id } = request.params;

  setAuditMeta(
    request,
    {
      module: MODULE,
      event: SYSTEM_LOCALISATION_SETTINGS,
      action: UPDATED_STATUS,
    },
    id,
  );

  const { result, audit } = await LocalisationService.updateStatus(request, id);

  await request.server.withAuditLogging({
    request,
    modelMapping: {
      Localisation: audit,
    },
  });

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: UPDATE_STATUS,
  });
};
