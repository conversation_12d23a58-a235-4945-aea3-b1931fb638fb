import { Button } from '@/components/base/inputs/button';
import { RouterLink } from '@/components/base/navigation/router-link';
import { Typography } from '@/components/base/typography';
import { useAuth } from '@/hooks/auth/use-auth.hook';
import { useRouter } from '@/hooks/navigation/use-router.hook';
import ROUTES from '@/router/routes';
import { defaultLoginValues, loginSchema, type LoginFormValues } from '@/schemas/auth';
import { authService } from '@/services/auth/auth.service';
import { isSuccessResponse } from '@/types/api-responses.type';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Box,
  Checkbox,
  Container,
  Divider,
  FormControlLabel,
  Grid,
  Link,
  Stack,
  useTheme,
} from '@mui/material';
import Image from 'next/image';
import { useCallback, useState, type JSX } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  DemoCredentials,
  EmailField,
  FormContainer,
  FormError,
  FormHeader,
  PasswordField,
  SubmitButton,
} from '../AuthFormComponents';

// OAuth provider types and data
interface OAuthProvider {
  id: 'google' | 'github';
  name: string;
  logo: string;
}

const oAuthProviders: OAuthProvider[] = [
  {
    id: 'google',
    name: 'Google',
    logo: '/placeholders/logo/google-icon.svg',
  },
  {
    id: 'github',
    name: 'Github',
    logo: '/placeholders/logo/github-icon.svg',
  },
];

/**
 * LoginForm is a component that renders a form for users to sign in.
 *
 * It includes:
 * - OAuth sign-in options (Google, GitHub)
 * - Email and password fields
 * - "Keep me signed in" checkbox
 * - "Recover password" link
 * - Form validation
 *
 * @example
 * ```tsx
 * <LoginForm />
 * ```
 */
export function LoginForm(): JSX.Element {
  const router = useRouter();
  const { checkSession } = useAuth();
  const [isPending, setIsPending] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<LoginFormValues>({
    defaultValues: defaultLoginValues,
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = useCallback(
    async (values: LoginFormValues): Promise<void> => {
      setIsPending(true);

      const response = await authService.signInWithPassword(values);

      if (!isSuccessResponse(response)) {
        setError('root', {
          type: 'server',
          message: response.message ?? 'Authentication failed',
        });
        setIsPending(false);
        return;
      }

      await checkSession();

      router.refresh();
    },
    [router, setError, checkSession]
  );

  // Password visibility is now handled inside the PasswordField component

  const { t } = useTranslation();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Update GitHub logo based on theme
  const updatedOAuthProviders = oAuthProviders.map((provider) => {
    let logo = provider.logo;
    if (provider.id === 'github') {
      logo = isDarkMode
        ? '/placeholders/logo/github-icon-light.svg'
        : '/placeholders/logo/github-icon.svg';
    }
    return {
      ...provider,
      logo,
    };
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <FormHeader
        title="Sign in"
        subtitle="Access your account and continue your journey"
      />
      <FormContainer>
        {/* OAuth Providers */}
        <Container maxWidth="sm">
          <Stack
            justifyContent="center"
            direction={{ xs: 'column', sm: 'row' }}
            spacing={1}
          >
            {updatedOAuthProviders.map((provider) => (
              <Button
                fullWidth
                disabled={isPending}
                sx={{
                  whiteSpace: 'nowrap',
                }}
                variant="outlined"
                color="secondary"
                key={provider.id}
                startIcon={
                  <Image
                    height={24}
                    width={24}
                    alt={provider.name}
                    src={provider.logo}
                  />
                }
              >
                Sign in with {provider.name}
              </Button>
            ))}
          </Stack>
        </Container>

        {/* Divider */}
        <Divider flexItem>
          <Typography variant="subtitle1">or with email</Typography>
        </Divider>

        {/* Form Fields */}
        <Container maxWidth="sm">
          <Grid
            container
            spacing={2}
          >
            {/* Email Field */}
            <EmailField
              register={register}
              errors={errors}
            />

            {/* Password Field */}
            <PasswordField
              register={register}
              errors={errors}
            />

            {/* Remember Me & Forgot Password */}
            <Grid size={12}>
              <Box
                alignItems="center"
                display="flex"
                justifyContent="space-between"
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      name="keepSignedIn"
                      color="primary"
                    />
                  }
                  label={<Typography variant="body1">{t('Keep me signed in')}</Typography>}
                />
                <Link
                  component={RouterLink}
                  href={ROUTES.AUTH['RESET_PASSWORD']}
                  underline="hover"
                >
                  {t('Recover password')}
                </Link>
              </Box>
            </Grid>

            {/* Submit Button */}
            <SubmitButton
              label="Sign in"
              isPending={isPending}
            />

            {/* Error Message */}
            {errors.root && <FormError error={errors.root.message} />}

            {/* Demo Credentials */}
            <DemoCredentials />
          </Grid>
        </Container>
      </FormContainer>
    </form>
  );
}

export default LoginForm;
