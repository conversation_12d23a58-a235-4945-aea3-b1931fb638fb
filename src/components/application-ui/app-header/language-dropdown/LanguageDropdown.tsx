import { Button } from '@/components/base/inputs/button';
import { LANGUAGE_OPTIONS, type LanguageOption } from '@/constants/languages.constant';
import { ListItemIcon, ListItemText, Menu, MenuItem } from '@mui/material';
import { useCallback, useState, type MouseEvent } from 'react';
import ReactCountryFlag from 'react-country-flag';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import type {
  LanguageButtonProps,
  LanguageDropdownProps,
  LanguageMenuProps,
} from './LanguageDropdown.type';
import { getLanguageMenuStyles } from './styles/LanguageDropdown.style';

/**
 * Language dropdown component for the header
 */
const LanguageDropdown = ({ color = 'inherit', sx = {} }: LanguageDropdownProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { t, i18n } = useTranslation();

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChange = useCallback(
    async (language: Language) => {
      await i18n.changeLanguage(language);
      toast.success(
        t('Language changed to {{language}}', { language: LANGUAGE_OPTIONS[language].label }),
        {
          position: 'bottom-center',
        }
      );
      handleClose();
    },
    [i18n, t]
  );

  const currentLanguage = i18n.language as Language;
  const currentFlag = LANGUAGE_OPTIONS[currentLanguage]?.icon || 'US';

  return (
    <>
      <LanguageButton
        color={color}
        sx={sx}
        onClick={handleClick}
        flag={currentFlag}
      />
      <LanguageMenu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        currentLanguage={currentLanguage}
        onLanguageChange={handleChange}
      />
    </>
  );
};

/**
 * Button component for the language dropdown
 */
const LanguageButton = ({ color, sx, onClick, flag }: LanguageButtonProps) => {
  const { t } = useTranslation();
  return (
    <Button
      title={t('Switch language')}
      id="language-button"
      color={color}
      aria-controls="language-menu"
      aria-haspopup="true"
      onClick={onClick}
      sx={sx}
      customStyle="icon"
    >
      <ReactCountryFlag
        countryCode={flag}
        svg
        style={{ width: '1.5em', height: '1.5em' }}
      />
    </Button>
  );
};

/**
 * Menu component for the language dropdown
 */
const LanguageMenu = ({
  anchorEl,
  open,
  onClose,
  currentLanguage,
  onLanguageChange,
}: LanguageMenuProps) => (
  <Menu
    id="language-menu"
    anchorEl={anchorEl}
    open={open}
    onClose={onClose}
    slotProps={{
      list: {
        'aria-labelledby': 'language-button',
        sx: getLanguageMenuStyles(),
      },
    }}
    transformOrigin={{ vertical: 'top', horizontal: 'right' }}
    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
  >
    {(Object.entries(LANGUAGE_OPTIONS) as [Language, LanguageOption][]).map(([lang, option]) => (
      <MenuItem
        key={lang}
        onClick={() => onLanguageChange(lang)}
        selected={currentLanguage === lang}
      >
        <ListItemIcon>
          <ReactCountryFlag
            style={{ width: '2em', height: '2em' }}
            countryCode={option.icon}
            svg
          />
        </ListItemIcon>
        <ListItemText
          sx={{ pl: 1 }}
          primary={option.label}
        />
      </MenuItem>
    ))}
  </Menu>
);

export default LanguageDropdown;
