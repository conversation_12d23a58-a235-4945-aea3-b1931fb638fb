import {
  createCustomError,
  createModuleErrors,
  createValidationError,
} from '#src/utils/error.util.js';
import { describe, expect, it } from 'vitest';

describe('createCustomError', () => {
  it('should create a custom error with the correct name format based on the module name', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'An error occurred';
    const statusCode = 400;
    const moduleName = 'Test';

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError();

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.code).toBe('CUSTOM_ERROR');
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.message).toBe('An error occurred');
    expect(errorInstance.statusCode).toBe(statusCode);
  });

  it('should include additional metadata when provided as the last argument', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'An error occurred: %s';
    const statusCode = 400;
    const moduleName = 'Test';
    const metaData = { detail: 'Additional error details' };

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError('Test error message', metaData);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.message).toBe('An error occurred: Test error message');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual(metaData);
  });

  it('should not include metadata if the last argument is not an object', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'An error occurred: %s';
    const statusCode = 400;
    const moduleName = 'Test';

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError('Test error message', 1);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.message).toBe('An error occurred: Test error message 1');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual({});
  });

  it('should correctly instantiate a custom error with a message template and arguments', () => {
    const code = 'TEMPLATE_ERROR';
    const message = 'An error occurred: %s';
    const statusCode = 500;
    const moduleName = 'Template';

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError('Template argument');

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TemplateModuleError');
    expect(errorInstance.message).toBe('An error occurred: Template argument');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual({});
  });
});

describe('createModuleErrors', () => {
  it('should create an object with error factory functions for all defined errors', () => {
    const moduleName = 'Test';
    const errorDefs = {
      notFound: ['10002', 'Data not found. %s', 404],
      unauthorised: ['10004', 'Forbidden', 403],
    };

    const errors = createModuleErrors(moduleName, errorDefs);

    // Verify all error keys are present
    expect(Object.keys(errors)).toEqual(['notFound', 'unauthorised']);

    // Verify each error factory generates the correct error
    const notFoundError = errors.notFound('UUID');

    expect(notFoundError).toBeInstanceOf(Error);
    expect(notFoundError.code).toBe('10002');
    expect(notFoundError.message).toBe('Data not found. UUID');
    expect(notFoundError.statusCode).toBe(404);
    expect(notFoundError.name).toBe('TestModuleError');

    // Verify each error factory generates the correct error
    const unauthorisedError = errors.unauthorised();

    expect(unauthorisedError).toBeInstanceOf(Error);
    expect(unauthorisedError.code).toBe('10004');
    expect(unauthorisedError.message).toBe('Forbidden');
    expect(unauthorisedError.statusCode).toBe(403);
    expect(unauthorisedError.name).toBe('TestModuleError');
  });
});

describe('createCustomError', () => {
  const mockValidationErrors = [
    {
      instancePath: [],
      schemaPath: '',
      keywoard: '',
      params: {},
      message: '',
    },
  ];

  it('should create a validation error with default message', () => {
    const errorInstance = createValidationError(mockValidationErrors);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.message).toBe('Validation Error');
    expect(errorInstance.statusCode).toBe(400);
    expect(errorInstance.code).toBe('VALIDATION_ERROR');
  });

  it('should create a validation error with custom message if provided', () => {
    const mockErrorMessage = 'Test error message';

    const errorInstance = createValidationError(mockValidationErrors, mockErrorMessage);

    expect(errorInstance.message).toBe(mockErrorMessage);
  });

  it('should handle single validation error object', () => {
    const errorInstance = createValidationError(mockValidationErrors[0]);

    expect(errorInstance.validation).toEqual(mockValidationErrors);
  });

  it('should handle array validation error object', () => {
    const errorInstance = createValidationError(mockValidationErrors);

    expect(errorInstance.validation).toEqual(mockValidationErrors);
  });
});
