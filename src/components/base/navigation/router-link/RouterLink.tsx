import NextLink from 'next/link';
import { forwardRef } from 'react';
import type { RouterLinkProps } from './RouterLink.type';

/**
 * A wrapper around `next/link`'s `Link` component that replaces `[accessId]`
 * with a real value.
 *
 * @example
 * ```tsx
 * <RouterLink href="/[accessId]/dashboard">Dashboard</RouterLink>
 * ```
 */
export const RouterLink = forwardRef<HTMLAnchorElement, RouterLinkProps>(
  ({ href, ...props }, ref) => {
    const resolvedHref = href.replace('[accessId]', '123456789012'); // Replace '123456789012' with the real value

    return (
      <NextLink
        ref={ref}
        href={resolvedHref}
        {...props}
      />
    );
  }
);

RouterLink.displayName = 'RouterLink';

export default RouterLink;
