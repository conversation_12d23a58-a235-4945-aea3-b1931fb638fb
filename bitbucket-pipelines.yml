image: node:22

definitions:
  steps:
    - step: &installation-step
        name: Install dependencies
        caches:
          - node
        script:
          - node -v # Verify Node.js version
          - npm install # Use npm install to generate lockfile
        artifacts:
          - node_modules/**
    - step: &build-and-test-step
        name: 'Build and Test'
        caches:
          - node
          - playwright
        script:
          - npx playwright install --with-deps
          - npm run test:coverage
        artifacts:
          - coverage/**
    - step: &lint-step
        name: 'Lint'
        script:
          - npm run lint
    - step: &analysis-step
        name: 'Perform SonarQube analysis'
        image: sonarsource/sonar-scanner-cli:10
        caches:
          - sonar
        clone:
          depth: full
        script:
          - sonar-scanner -Dsonar.branch.name=$BITBUCKET_BRANCH -Dproject.settings=config/sonar-project.properties
    - step: &staging-deployment-step
        name: 'Deployment to Staging'
        deployment: staging
        script:
          - echo "Your deployment to staging script goes here..."
    - step: &production-deployment-step
        name: 'Deployment to Production'
        deployment: production
        trigger: 'manual'
        script:
          - echo "Your deployment to production script goes here..."

  caches:
    node: ~/.npm
    sonar: ~/.sonar
    playwright: /root/.cache/ms-playwright

clone:
  depth: full

pipelines:
  branches:
    'main':
      - step: *installation-step
      - step: *build-and-test-step
      - step: *lint-step
      - step: *analysis-step

    'develop':
      - step: *installation-step
      - step: *build-and-test-step
      - step: *lint-step
      - step: *analysis-step

    ## Intended for testing purposes only
    # "feature/*":
    # - step: *build-and-test-step
    # - step: *lint-step
    # - step: *analysis-step

  pull-requests:
    '**':
      - step: *installation-step
      - step: *build-and-test-step
      - step: *lint-step
      - step: *analysis-step
