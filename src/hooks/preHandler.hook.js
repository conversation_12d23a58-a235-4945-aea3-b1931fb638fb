import { bypassAccessCheck, checkAccess, enforceIPWhitelist } from '#src/utils/access.util.js';
import fp from 'fastify-plugin';

/**
 * preHandler hook
 *
 * This hook is executed just before the handler is executed.
 * It's useful for performing operations that should happen right before
 * the route handler, such as input validation or authentication checks.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const preHandlerHook = (fastify, options) => {
  fastify.addHook('preHandler', async (request, reply) => {
    fastify.log.debug('Executing preHandler hook');
    // Add your preHandler logic here
    const routeAccess = request.routeOptions?.config?.access || {};
    const authAccess = request.authInfo?.authAccess;

    if (!bypassAccessCheck(request)) {
      if (!checkAccess(request, authAccess, routeAccess)) {
        return reply.code(403).send({ message: 'Access denied' });
      }

      if (authAccess === 'webhook' && routeAccess.ipWhitelist) {
        const ipAllowed = enforceIPWhitelist(request, routeAccess.ipWhitelist);
        if (!ipAllowed) {
          return reply.code(403).send({ message: 'IP not whitelisted' });
        }
      }
    }

    const routeConfig = request.routeOptions?.config;

    if (
      request.body &&
      ['POST', 'PUT', 'PATCH'].includes(request.method) &&
      request.headers['content-type']?.includes('application/json')
    ) {
      const ignoreFields = routeConfig?.sanitiseHtml?.ignoreFields || [];
      const customPurifyOpts = routeConfig?.sanitiseHtml?.options || {};
      request.body = fastify.sanitiseData(request.body, ignoreFields, customPurifyOpts);
    }
  });
};

export default fp(preHandlerHook, {
  name: 'preHandlerHook',
});
