import { AvatarTitleDescriptionStacked } from '@/components/base/data-display/avatar';
import { Button } from '@/components/base/inputs/button';
import Typography from '@/components/base/typography';
import { useAuth } from '@/hooks/auth/use-auth.hook';
import { authService } from '@/services/auth';
import LogoutTwoToneIcon from '@mui/icons-material/LogoutTwoTone';
import { Box, Divider, Menu, MenuItem } from '@mui/material';
import { useTranslation } from 'react-i18next';
import type {
  HeaderUserDropdownProps,
  MenuOptionsProps,
  SignOutButtonProps,
} from './HeaderUserDropdown.type';
import {
  getMenuOptionsSectionStyles,
  getSignOutButtonSectionStyles,
  getUserProfileSectionStyles,
} from './styles/HeaderUserDropdown.style';

const MENU_ITEMS = ['my profile', 'change password'] as const;

/**
 * User dropdown component for the header
 */
export const HeaderUserDropdown = ({
  anchorEl,
  open,
  onClose,
  anchorOrigin,
  transformOrigin,
  ...otherProps
}: HeaderUserDropdownProps) => {
  const handleSignOut = () => {
    onClose();
    authService.signOut();
  };

  return (
    <Menu
      id="header-menu"
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      anchorOrigin={anchorOrigin || { vertical: 'top', horizontal: 'right' }}
      transformOrigin={transformOrigin || { vertical: 'top', horizontal: 'right' }}
      slotProps={{
        list: {
          'aria-labelledby': 'header-button',
          sx: { p: 0 },
        },
      }}
      {...otherProps}
    >
      <UserProfile />
      <Divider />
      <MenuOptions onClose={onClose} />
      <Divider />
      <SignOutButton onClick={handleSignOut} />
    </Menu>
  );
};

/**
 * User profile section of the dropdown
 */
const UserProfile = () => {
  const { user } = useAuth();
  const { t } = useTranslation();

  return (
    <Box sx={getUserProfileSectionStyles()}>
      <AvatarTitleDescriptionStacked
        title={user?.username ?? t('user name')}
        description={user?.role ?? t('user role')}
        avatarSrc={user?.avatar}
      />
    </Box>
  );
};

/**
 * Menu options section of the dropdown
 */
const MenuOptions = ({ onClose }: MenuOptionsProps) => {
  const { t } = useTranslation();

  return (
    <Box sx={getMenuOptionsSectionStyles()}>
      {MENU_ITEMS.map((item) => (
        <MenuItem
          key={item}
          onClick={onClose}
        >
          <Typography caseTransform="sentenceCase">{t(item)}</Typography>
        </MenuItem>
      ))}
    </Box>
  );
};

/**
 * Sign out button section of the dropdown
 */
const SignOutButton = ({ onClick }: SignOutButtonProps) => {
  const { t } = useTranslation();

  return (
    <Box sx={getSignOutButtonSectionStyles()}>
      <Button
        color="error"
        startIcon={<LogoutTwoToneIcon />}
        fullWidth
        onClick={onClick}
      >
        {t('sign out')}
      </Button>
    </Box>
  );
};

export default HeaderUserDropdown;
