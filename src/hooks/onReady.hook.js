import { clearCache, getCache, getCacheKeysWithPrefix } from '#src/utils/cache.util.js';
import { execPath } from 'process';
import fp from 'fastify-plugin';
import geoip from 'geoip-lite';
import { spawn } from 'child_process';

/**
 * onReady hook
 *
 * This hook is executed when the server is ready to accept connections.
 * It can be used for performing operations after the server is fully initialized.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onReadyHook = (fastify, options) => {
  fastify.addHook('onReady', async () => {
    fastify.log.debug('Executing onReady hook');

    // Add your onReady logic here
    if (fastify.config.KAFKA) {
      // Retrieve the configurations in System Settings > App Center such as Kafka Brokers, Topic Names, Total Partitions etc.
      const kafkaInfo = { topicName: 'audit-trails', topicPartitions: 50 };
      await createKafkaTopics(fastify, kafkaInfo);
      // Start Kafka retry worker every 10 seconds
      setInterval(() => retryKafka(fastify, kafkaInfo), 10000);
    }

    await updateGeoIPDatabase(fastify);
  });
};

export default fp(onReadyHook, {
  name: 'onReadyHook',
});

/**
 * Creates a Kafka topic if it does not exist.
 *
 * @param {import('fastify').FastifyInstance} fastify - The Fastify instance with Kafka and Redis decorators.
 * @param {Object} kafkaInfo - Information about the Kafka topic to be created.
 * @returns {Promise<void>} - A promise that resolves when the Kafka topic is created or already exists.
 */
const createKafkaTopics = async (fastify, kafkaInfo) => {
  if (fastify.kafka?.admin) {
    try {
      const admin = fastify.kafka.admin;
      const existingTopics = await admin.listTopics();

      if (!existingTopics.includes(kafkaInfo.topicName)) {
        await admin.createTopics({
          topics: [
            {
              topic: kafkaInfo.topicName,
              numPartitions: kafkaInfo.topicPartitions,
              replicationFactor: 1,
            },
          ],
        });
        fastify.log.info(
          `Kafka topic "${kafkaInfo.topicName}" created with ${kafkaInfo.topicPartitions} partitions.`,
        );
      } else {
        fastify.log.info(`Kafka topic "${kafkaInfo.topicName}" already exists.`);
      }
    } catch (err) {
      fastify.log.error(err, 'Error during Kafka topic setup');
    }
  }
};

/**
 * A function to retry sending failed Kafka messages from Redis to Kafka.
 *
 * @param {import('fastify').FastifyInstance} fastify - The Fastify instance with Kafka and Redis decorators.
 * @param {Object} kafkaInfo - Information about the Kafka topic to be created.
 * @returns {Promise<void>} - A promise that resolves when all failed Kafka messages are retried.
 */
const retryKafka = async (fastify, kafkaInfo) => {
  // Fetch failed Kafka messages from Redis and send them to Kafka
  const keys = await getCacheKeysWithPrefix(fastify.redis, 'failed-kafka-message:*');
  if (!keys || keys.length === 0) {
    return;
  }

  for (const key of keys) {
    const message = await getCache(fastify.redis, key);
    if (!message) {
      continue;
    }
    fastify.log.info('Kafka resend from worker');
    try {
      await fastify.kafka.producer.send({
        topic: kafkaInfo.topicName,
        messages: message,
      });
      clearCache(fastify.redis, key);
    } catch (error) {
      fastify.log.debug(error, 'Kafka send failed from worker');
    }
  }
};

/**
 * Updates the GeoIP database and reloads the data into memory.
 *
 * This function executes the `updatedb.js` script from the `geoip-lite` package to update the GeoIP database.
 * It then reloads the updated data into memory using the `reloadData` method from the `geoip-lite` package.
 *
 * @param {import('fastify').FastifyInstance} fastify - The Fastify instance with access to the application's configuration and logging utilities.
 * @returns {Promise<void>} - A promise that resolves when the GeoIP database update and data reload are complete.
 */
const updateGeoIPDatabase = async (fastify) => {
  const licenseKey = fastify.config.GEOIP_LICENSE_KEY;
  if (!licenseKey) {
    fastify.log.warn('GeoIP license key missing. Skipping update.');
    return;
  }

  const updatedb = spawn(execPath, [
    './node_modules/geoip-lite/scripts/updatedb.js',
    `license_key=${fastify.config.GEOIP_LICENSE_KEY}`,
  ]);

  updatedb.stdout.on('data', (data) => {
    fastify.log.info(`GeoIP database updated: ${data}`);
  });

  updatedb.stderr.on('data', (data) => {
    fastify.log.error(`GeoIP update failed: ${data}`);
  });

  updatedb.on('close', (code) => {
    if (code === 0) {
      geoip.reloadData(() => {
        fastify.log.info('GeoIP data reloaded into memory.');
      });
    }
  });
};
