import fp from 'fastify-plugin';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { formatSuccessResponse } from '#src/utils/response.util.js';

import successMessage from '#src/utils/success-message.util.js';

/**
 * Decorates the Fastify reply object with a 'success' method for standardized success responses.
 *
 * @param {Object} fastify - The Fastify instance.
 * @param {Object} opts - Plugin options (not used in this function).
 * @returns {void}
 */
export default fp(async (fastify, opts) => {
  /**
   * Sends a formatted success response.
   *
   * @param {string} module - The module name for the success message.
   * @param {string} type - The type of success operation.
   * @param {*} [data=null] - The data to be included in the response.
   * @param {Object} [meta={}] - Additional metadata for the response.
   * @param {number} [statusCode=200] - The HTTP status code for the response.
   * @returns {Object} The formatted success response.
   */
  fastify.decorateReply(
    'success',
    function (module, type, data = null, meta = {}, statusCode = 200) {
      const message = successMessage.getTemplate(module, type, data);
      const code = type === CoreConstant.MODULE_METHODS.CREATE ? 201 : statusCode;
      return this.code(code).send(formatSuccessResponse(message, data, meta));
    },
  );
});
