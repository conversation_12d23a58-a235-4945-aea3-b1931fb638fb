import { getByXPath } from '@/utils/xpath-selector.util';
import { DataGridPremium, Toolbar, type GridPremiumSlotsComponent } from '@mui/x-data-grid-premium';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, screen, waitFor } from 'storybook/test';
import CustomFilterButton from './CustomFilterButton';

/**
 * Storybook metadata configuration for the CustomFilterButton component.
 */
const meta = {
  component: CustomFilterButton,
  title: 'Components/base/data-display/datagrid/CustomFilterButton',
  tags: ['autodocs'],
  decorators: [
    (Story) => {
      const CustomToolbarComponent = () => (
        <Toolbar>
          <Story />
        </Toolbar>
      );

      const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
        toolbar: CustomToolbarComponent,
      };
      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={defaultSlotProps}
        />
      );
    },
  ],
} satisfies Meta<typeof CustomFilterButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomFilterButton component.
 */
export const Default: Story = {
  play: async ({ canvas, userEvent, step }) => {
    await step('Able to show tooltip', async () => {
      await userEvent.hover(canvas.getByText('common.button.filters'));

      const tooltip = await waitFor(() =>
        getByXPath(
          `//div[@role="tooltip" and contains(@class, "MuiTooltip-popper") and contains(., "common.label.selectFilters")]`,
          document
        )
      );

      expect(tooltip).toBeInTheDocument();
    });

    await step('Click filters button', async () => {
      await userEvent.click(canvas.getByText('common.button.filters'));
    });

    await step('Filter is shown when clicked', async () => {
      const addIcon = await waitFor(() => screen.getByTestId('AddIcon'));
      expect(addIcon).toBeInTheDocument();

      const removeIcon = await waitFor(() => screen.getByTestId('DeleteIcon'));
      expect(removeIcon).toBeInTheDocument();
    });

    await step('Filter badge count is shown', async () => {
      userEvent.type(screen.getByPlaceholderText('Filter value'), 'Test');

      const badge = await waitFor(() =>
        getByXPath('//span[contains(@class, "MuiBadge-badge") and text()="1"]', document)
      );
      expect(badge).toBeInTheDocument();
    });

    await step('Filter can be closed with escape button', async () => {
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(screen.queryByPlaceholderText('Search')).not.toBeInTheDocument();
      });
    });
  },
};
