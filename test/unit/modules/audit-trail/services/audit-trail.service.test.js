import * as AuditTrailService from '#src/modules/audit-trail/services/audit-trail.service.js';
import { describe, expect, it, vi } from 'vitest';
import { AuditTrailError } from '#src/modules/audit-trail/errors/index.js';
import { AuditTrailRepository } from '#src/modules/audit-trail/repository/index.js';
import { BulkJobRepository } from '#src/modules/bulk-job/repository/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';

vi.mock('#src/modules/audit-trail/repository/index.js', () => ({
  AuditTrailRepository: {
    findAll: vi.fn(),
    findById: vi.fn(),
  },
}));

vi.mock('#src/modules/bulk-job/repository/index.js', () => ({
  BulkJobRepository: {
    create: vi.fn(),
  },
}));

describe('AuditTrail Service', () => {
  describe('index', () => {
    it('should use default date range if no timestamps are provided', async () => {
      const request = {
        query: {},
      };

      const mockedResult = { rows: ['mocked data'] };
      AuditTrailRepository.findAll.mockResolvedValue(mockedResult);

      const result = await AuditTrailService.index(request);

      // Ensure fallback logic is tested — not exact value match due to Date precision
      expect(AuditTrailRepository.findAll).toHaveBeenCalledWith(
        request,
        expect.objectContaining({
          startDateTime: expect.any(Date),
        }),
      );

      expect(result).toBe(mockedResult);
    });

    it('should call findAll with correct parameters and return result', async () => {
      const request = {
        query: {
          filter_timestamp_gte: '2025-04-01T00:00:00Z',
          filter_timestamp_lte: '2025-04-30T00:00:00Z',
        },
      };
      const mockedResult = { rows: ['mocked data'] };
      AuditTrailRepository.findAll.mockResolvedValue(mockedResult);

      const result = await AuditTrailService.index(request);

      expect(AuditTrailRepository.findAll).toHaveBeenCalledWith(request, {
        startDateTime: new Date(request.query.filter_timestamp_gte),
      });
      expect(result).toBe(mockedResult);
    });

    it('should throw an error if the date range crosses months', async () => {
      const request = {
        query: {
          filter_timestamp_gte: '2025-03-01T00:00:00Z',
          filter_timestamp_lte: '2025-04-30T00:00:00Z',
        },
      };

      await expect(AuditTrailService.index(request)).rejects.toThrowError(
        AuditTrailError.invalidDate(),
      );
    });

    it('should throw an unknown error if findAll returns an error', async () => {
      const request = {
        query: {
          filter_timestamp_gte: '2025-04-01T00:00:00Z',
          filter_timestamp_lte: '2025-04-30T00:00:00Z',
        },
      };
      const mockedError = { error: 'some error' };
      AuditTrailRepository.findAll.mockResolvedValue(mockedError);

      await expect(AuditTrailService.index(request)).rejects.toThrow(
        CoreError.unknownError(mockedError.error),
      );
    });

    it('should throw not found error if no data is returned', async () => {
      const request = {
        query: {
          filter_timestamp_gte: '2025-04-01T00:00:00Z',
          filter_timestamp_lte: '2025-04-30T00:00:00Z',
        },
      };
      const emptyResult = { rows: [] };
      AuditTrailRepository.findAll.mockResolvedValue(emptyResult);

      await expect(AuditTrailService.index(request)).rejects.toThrow(
        CoreError.notFound(MODULE_NAMES.AUDIT_TRAIL),
      );
    });
  });

  describe('view', () => {
    it('should call findById and return result', async () => {
      const request = { query: {} };
      const id = 'some-id';
      const mockedResult = ['mocked data'];
      AuditTrailRepository.findById.mockResolvedValue(mockedResult);

      const result = await AuditTrailService.view(request, id);

      expect(AuditTrailRepository.findById).toHaveBeenCalledWith(request, id);
      expect(result).toBe(mockedResult[0]);
    });

    it('should throw not found error if no data is found for id', async () => {
      const request = { query: {} };
      const id = 'some-id';
      const emptyResult = [];
      AuditTrailRepository.findById.mockResolvedValue(emptyResult);

      await expect(AuditTrailService.view(request, id)).rejects.toThrow(
        CoreError.notFound(MODULE_NAMES.AUDIT_TRAIL),
      );
    });
  });

  describe('exportAuditTrail', () => {
    it('should call create with correct parameters and return result', async () => {
      const request = {
        query: { foo: 'bar' },
        server: {},
        entity: { id: 'entity-123' },
        authInfo: {},
      };
      const mockedJob = { jobId: '12345' };
      BulkJobRepository.create.mockResolvedValue(mockedJob);

      const result = await AuditTrailService.exportAuditTrail(request);

      expect(BulkJobRepository.create).toHaveBeenCalledWith(
        request.server,
        {
          entityId: request.entity.id,
          type: 'export',
          model: 'audit_trail',
          title: 'Export audit trails',
          parameters: request.query || {},
          status: 'pending',
        },
        request.authInfo,
      );
      expect(result).toBe(mockedJob);
    });

    it('should throw an unknown error if the create call returns an error', async () => {
      const request = {
        query: { foo: 'bar' },
        server: {},
        entity: { id: 'entity-123' },
      };
      const mockedError = { error: 'some error' };
      BulkJobRepository.create.mockResolvedValue(mockedError);

      await expect(AuditTrailService.exportAuditTrail(request)).rejects.toThrow(
        CoreError.unknownError(mockedError.error),
      );
    });
  });
});
