import { stringAvatar } from '@/components/base/data-display/avatar';
import { Button } from '@/components/base/inputs/button';
import { useAuth } from '@/hooks/auth/use-auth.hook';
import { Avatar, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { getUserProfileButtonStyles } from '../Buttons.style';
import type { UserProfileButtonProps } from '../Buttons.type';

/**
 * User profile button component for the header
 */
export const UserProfileButton = ({ popover }: UserProfileButtonProps) => {
  const theme = useTheme();
  const { user } = useAuth();
  const { t } = useTranslation();

  return (
    <Button
      title={t('User profile')}
      id="profile-button"
      sx={getUserProfileButtonStyles(theme)}
      color="inherit"
      aria-controls={popover.open ? 'profile-menu' : undefined}
      aria-haspopup="true"
      aria-expanded={popover.open ? 'true' : undefined}
      onClick={popover.handleOpen}
      ref={popover.anchorRef as any}
      customStyle="icon"
    >
      <Avatar
        alt={user?.username}
        src={user?.avatar}
        {...stringAvatar(user?.username ?? 'User', theme, {
          borderRadius: 'inherit',
          height: 36,
          width: 36,
        })}
      />
    </Button>
  );
};

export default UserProfileButton;
