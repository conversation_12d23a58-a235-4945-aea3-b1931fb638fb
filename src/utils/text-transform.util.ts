import type { ChangeCaseTransform } from '@/components/base/typography/Typography.type';
import * as changeCase from 'change-case';

/**
 * Transforms a string using the change-case library
 * @param text The text to transform
 * @param transform The transformation to apply
 * @returns The transformed text
 */
export const transformText = (text: string, transform: ChangeCaseTransform): string => {
  switch (transform) {
    case 'camelCase':
      return changeCase.camelCase(text);
    case 'capitalCase':
      return changeCase.capitalCase(text);
    case 'constantCase':
      return changeCase.constantCase(text);
    case 'dotCase':
      return changeCase.dotCase(text);
    case 'kebabCase':
      return changeCase.kebabCase(text);
    case 'lowercase':
      return text.toLowerCase();
    case 'noCase':
      return changeCase.noCase(text);
    case 'none':
      return text;
    case 'pascalCase':
      return changeCase.pascalCase(text);
    case 'pascalSnakeCase':
      return changeCase.pascalSnakeCase(text);
    case 'pathCase':
      return changeCase.pathCase(text);
    case 'sentenceCase':
      return changeCase.sentenceCase(text);
    case 'snakeCase':
      return changeCase.snakeCase(text);
    case 'trainCase':
      return changeCase.trainCase(text);
    case 'uppercase':
      return text.toUpperCase();
    default:
      return text;
  }
};

export default transformText;
