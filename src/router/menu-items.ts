import type { ChangeCaseTransform } from '@/components/base/typography';
import { type ReactNode } from 'react';
import ROUTES from './routes';

/**
 * Menu item structure for the sidebar navigation
 */
export interface MenuItem {
  /**
   * The title of the menu item
   */
  title: string;

  /**
   * The path to navigate to when the menu item is clicked
   */
  path?: string;

  /**
   * The icon to display next to the menu item
   */
  icon?: ReactNode;

  /**
   * Whether the menu item is a header (section title)
   */
  isHeader?: boolean;

  /**
   * Submenu items
   */
  items?: MenuItem[];

  /**
   * Text transformation for the menu item title
   */
  caseTransform?: ChangeCaseTransform;
}

/**
 * Generate menu items with translations
 *
 * This can be extended to include role-based filtering in the future.
 *
 * @param t Translation function
 * @returns Array of menu items
 */
export const getMenuItems = (t: (token: string) => string): MenuItem[] => {
  return [
    {
      title: t('general'),
      isHeader: true,
      items: [
        {
          title: t('dashboard'),
          path: ROUTES.DASHBOARD,
        },
      ],
    },
    {
      title: t('user management'),
      isHeader: true,
      items: [
        {
          title: t('user'),
          path: ROUTES.USER_MANAGEMENT.USER,
        },
        {
          title: t('role and policy'),
          path: '',
        },
        {
          title: t('department'),
          path: '',
        },
      ],
    },
    {
      title: t('member management'),
      isHeader: true,
      items: [
        {
          title: t('member account management'),
          items: [
            {
              title: t('member account'),
              path: '',
            },
            {
              title: t('member verification'),
              path: '',
            },
          ],
        },
        {
          title: t('member segmentation'),
          items: [
            {
              title: t('risk group'),
              path: '',
            },
            {
              title: t('VIP tier'),
              caseTransform: 'none',
              path: '',
            },
          ],
        },
      ],
    },
    {
      title: t('log & monitoring'),
      isHeader: true,
      items: [
        {
          title: t('audit trail'),
          path: '',
        },
        {
          title: t('event'),
          items: [
            {
              title: t('manage monitor'),
              path: '',
            },
            {
              title: t('triggered event'),
              path: '',
            },
          ],
        },
      ],
    },
    {
      title: t('setting'),
      isHeader: true,
      items: [
        {
          title: t('general setting'),
          items: [
            {
              title: t('currency'),
              path: '',
            },
            {
              title: t('theme'),
              path: '',
            },
          ],
        },
        {
          title: t('security control'),
          items: [
            {
              title: t('safety'),
              path: '',
            },
            {
              title: t('access control'),
              path: '',
            },
          ],
        },
      ],
    },
  ];
};

export default getMenuItems;
