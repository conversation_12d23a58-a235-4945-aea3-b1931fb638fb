/** @type {import('next').NextConfig} */
const config = {
  // Environment variables
  env: {
    npm_package_version: process.env.npm_package_version,
  },

  // Build configuration
  output: 'standalone', // Optimized for container deployments
  poweredByHeader: false, // Remove X-Powered-By header for security
  reactStrictMode: true, // Enable strict mode for better development
  compress: true, // Enable gzip compression
  generateEtags: true, // Enable ETag generation for caching

  // Image optimization
  images: {
    domains: [], // Add allowed external domains here
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ['image/webp'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: false,
  },

  // Transpilation
  transpilePackages: ['@mui/x-charts-pro'],

  // Webpack configuration
  webpack(config, { dev, isServer }) {
    // SVG handling
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    // Optimize bundle size in production
    if (!dev && !isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          minChunks: 1,
          maxAsyncRequests: 30,
          maxInitialRequests: 30,
          cacheGroups: {
            defaultVendors: {
              test: /[\\/]node_modules[\\/]/,
              priority: -10,
              reuseExistingChunk: true,
            },
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
          },
        },
      };
    }

    return config;
  },

  // Redirects
  async redirects() {
    return [
      {
        source: '/',
        destination: '/login',
        permanent: true,
      },
    ];
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          { key: 'X-DNS-Prefetch-Control', value: 'on' },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload',
          },
          { key: 'X-XSS-Protection', value: '1; mode=block' },
          { key: 'X-Frame-Options', value: 'SAMEORIGIN' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'Referrer-Policy', value: 'origin-when-cross-origin' },
          {
            key: 'Permissions-Policy',
            value: [
              'camera=()',
              'microphone=()',
              'geolocation=()',
              'interest-cohort=()',
              'payment=()',
              'usb=()',
              'magnetometer=()',
              'gyroscope=()',
              'accelerometer=()',
            ].join(', '),
          },
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-eval' 'unsafe-inline'",
              "style-src 'self' 'unsafe-inline'",
              "img-src 'self' data: blob: https:",
              "font-src 'self'",
              "connect-src 'self' https:",
              "media-src 'self'",
              "object-src 'none'",
              "frame-src 'self'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'self'",
              'block-all-mixed-content',
              'upgrade-insecure-requests',
            ].join('; '),
          },
        ],
      },
    ];
  },

  // Experimental features
  experimental: {
    // Enable modern optimizations
    optimizeCss: true,
    serverActions: {
      allowedOrigins: ['localhost:4000'], // Add your allowed origins
      bodySizeLimit: '2mb',
    },
    webVitalsAttribution: ['CLS', 'LCP'],
  },

  // Disable eslint during builds for CI/CD (use separate lint step)
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Enable detailed logging in development
  logging: {
    level: process.env.NODE_ENV === 'development' ? 'verbose' : 'error',
  },
};

// Enable source maps in production if needed
if (process.env.SOURCE_MAPS === 'true') {
  config.productionBrowserSourceMaps = true;
}

export default config;
