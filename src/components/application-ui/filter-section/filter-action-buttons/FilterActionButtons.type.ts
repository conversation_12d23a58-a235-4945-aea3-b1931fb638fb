import type { Pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/theme/colors';

/**
 * Props for the FilterActionButtons component
 */
export interface FilterActionButtonsProps {
  /**
   * Callback fired when the search button is clicked
   */
  onSearch: () => void;

  /**
   * Callback fired when the clear button is clicked
   */
  onClear: () => void;

  /**
   * Text for the search button
   * @default 'Search'
   */
  searchButtonText?: string;

  /**
   * Text for the clear button
   * @default 'Clear all'
   */
  clearButtonText?: string;

  /**
   * Color of the search button
   * @default 'primary'
   */
  searchButtonColor?: PaletteColorKey;

  /**
   * Whether to show the clear button
   * @default true
   */
  showClearButton?: boolean;
}
